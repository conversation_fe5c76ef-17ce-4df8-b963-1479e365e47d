# 项目基础信息Web服务配置文件

# Web服务配置
webservice.url=http://10.114.1.23:20080/service/projectbaseinfo
webservice.auth.code=4022025ii5d8jnm87ls89e878831001e
webservice.timeout=30000
webservice.retry.count=3

# 测试项目代码配置
test.project.codes=2506-321062-89-01-579366,2505-320324-89-01-254346,2506-320921-89-01-138659

# 输出配置
output.directory=./output
output.file.prefix=project_info_
output.file.format=txt

# 日志配置
log.level=INFO
log.file=./logs/application.log
log.max.size=10MB
log.max.files=5

# 网络配置
network.connect.timeout=10000
network.read.timeout=30000
network.proxy.enabled=false
network.proxy.host=
network.proxy.port=

# 系统配置
system.encoding=UTF-8
system.locale=zh_CN

# 测试配置
test.mock.enabled=true
test.integration.enabled=false
test.report.enabled=true
test.report.format=markdown

# 性能配置
performance.thread.pool.size=5
performance.batch.size=10
performance.delay.between.requests=1000
