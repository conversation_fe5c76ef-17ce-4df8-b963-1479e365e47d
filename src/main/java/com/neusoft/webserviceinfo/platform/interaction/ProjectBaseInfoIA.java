
package com.neusoft.webserviceinfo.platform.interaction;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "ProjectBaseInfoIA", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", wsdlLocation = "http://10.114.1.23:20080/service/projectbaseinfo?wsdl")
public class ProjectBaseInfoIA
    extends Service
{

    private final static URL PROJECTBASEINFOIA_WSDL_LOCATION;
    private final static WebServiceException PROJECTBASEINFOIA_EXCEPTION;
    private final static QName PROJECTBASEINFOIA_QNAME = new QName("http://interaction.platform.webserviceinfo.neusoft.com", "ProjectBaseInfoIA");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://10.114.1.23:20080/service/projectbaseinfo?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        PROJECTBASEINFOIA_WSDL_LOCATION = url;
        PROJECTBASEINFOIA_EXCEPTION = e;
    }

    public ProjectBaseInfoIA() {
        super(__getWsdlLocation(), PROJECTBASEINFOIA_QNAME);
    }

    public ProjectBaseInfoIA(WebServiceFeature... features) {
        super(__getWsdlLocation(), PROJECTBASEINFOIA_QNAME, features);
    }

    public ProjectBaseInfoIA(URL wsdlLocation) {
        super(wsdlLocation, PROJECTBASEINFOIA_QNAME);
    }

    public ProjectBaseInfoIA(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, PROJECTBASEINFOIA_QNAME, features);
    }

    public ProjectBaseInfoIA(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ProjectBaseInfoIA(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns ProjectBaseInfoIAPortType
     */
    @WebEndpoint(name = "ProjectBaseInfoIAHttpPort")
    public ProjectBaseInfoIAPortType getProjectBaseInfoIAHttpPort() {
        return super.getPort(new QName("http://interaction.platform.webserviceinfo.neusoft.com", "ProjectBaseInfoIAHttpPort"), ProjectBaseInfoIAPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ProjectBaseInfoIAPortType
     */
    @WebEndpoint(name = "ProjectBaseInfoIAHttpPort")
    public ProjectBaseInfoIAPortType getProjectBaseInfoIAHttpPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://interaction.platform.webserviceinfo.neusoft.com", "ProjectBaseInfoIAHttpPort"), ProjectBaseInfoIAPortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (PROJECTBASEINFOIA_EXCEPTION!= null) {
            throw PROJECTBASEINFOIA_EXCEPTION;
        }
        return PROJECTBASEINFOIA_WSDL_LOCATION;
    }

}
