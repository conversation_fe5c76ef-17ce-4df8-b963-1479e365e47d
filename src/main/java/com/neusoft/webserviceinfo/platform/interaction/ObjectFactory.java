
package com.neusoft.webserviceinfo.platform.interaction;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.neusoft.webserviceinfo.platform.interaction package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.neusoft.webserviceinfo.platform.interaction
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link CheckProjectByCode }
     * 
     */
    public CheckProjectByCode createCheckProjectByCode() {
        return new CheckProjectByCode();
    }

    /**
     * Create an instance of {@link QueryProjectAndItemInfoByProCode }
     * 
     */
    public QueryProjectAndItemInfoByProCode createQueryProjectAndItemInfoByProCode() {
        return new QueryProjectAndItemInfoByProCode();
    }

    /**
     * Create an instance of {@link ProjectBaseInfo }
     * 
     */
    public ProjectBaseInfo createProjectBaseInfo() {
        return new ProjectBaseInfo();
    }

    /**
     * Create an instance of {@link AddProjectInfo1Response }
     * 
     */
    public AddProjectInfo1Response createAddProjectInfo1Response() {
        return new AddProjectInfo1Response();
    }

    /**
     * Create an instance of {@link ProjectBaseInfoResponse }
     * 
     */
    public ProjectBaseInfoResponse createProjectBaseInfoResponse() {
        return new ProjectBaseInfoResponse();
    }

    /**
     * Create an instance of {@link QueryProjectAndItemInfoByProCodeResponse }
     * 
     */
    public QueryProjectAndItemInfoByProCodeResponse createQueryProjectAndItemInfoByProCodeResponse() {
        return new QueryProjectAndItemInfoByProCodeResponse();
    }

    /**
     * Create an instance of {@link ProjectBindingInfoResponse }
     * 
     */
    public ProjectBindingInfoResponse createProjectBindingInfoResponse() {
        return new ProjectBindingInfoResponse();
    }

    /**
     * Create an instance of {@link AddProjectInfo1 }
     * 
     */
    public AddProjectInfo1 createAddProjectInfo1() {
        return new AddProjectInfo1();
    }

    /**
     * Create an instance of {@link ProjectQueryInfoByCode }
     * 
     */
    public ProjectQueryInfoByCode createProjectQueryInfoByCode() {
        return new ProjectQueryInfoByCode();
    }

    /**
     * Create an instance of {@link AddProjectInfoResponse }
     * 
     */
    public AddProjectInfoResponse createAddProjectInfoResponse() {
        return new AddProjectInfoResponse();
    }

    /**
     * Create an instance of {@link ProjectQueryInfoByCodeResponse }
     * 
     */
    public ProjectQueryInfoByCodeResponse createProjectQueryInfoByCodeResponse() {
        return new ProjectQueryInfoByCodeResponse();
    }

    /**
     * Create an instance of {@link ProjectQueryInfoByKeyCode }
     * 
     */
    public ProjectQueryInfoByKeyCode createProjectQueryInfoByKeyCode() {
        return new ProjectQueryInfoByKeyCode();
    }

    /**
     * Create an instance of {@link ProjectQueryInfoByKeyCodeResponse }
     * 
     */
    public ProjectQueryInfoByKeyCodeResponse createProjectQueryInfoByKeyCodeResponse() {
        return new ProjectQueryInfoByKeyCodeResponse();
    }

    /**
     * Create an instance of {@link ProjectQueryInfoByTime }
     * 
     */
    public ProjectQueryInfoByTime createProjectQueryInfoByTime() {
        return new ProjectQueryInfoByTime();
    }

    /**
     * Create an instance of {@link ProjectQueryInfoByTimeResponse }
     * 
     */
    public ProjectQueryInfoByTimeResponse createProjectQueryInfoByTimeResponse() {
        return new ProjectQueryInfoByTimeResponse();
    }

    /**
     * Create an instance of {@link CheckProjectByCodeResponse }
     * 
     */
    public CheckProjectByCodeResponse createCheckProjectByCodeResponse() {
        return new CheckProjectByCodeResponse();
    }

    /**
     * Create an instance of {@link AddProjectInfo }
     * 
     */
    public AddProjectInfo createAddProjectInfo() {
        return new AddProjectInfo();
    }

    /**
     * Create an instance of {@link ProjectBindingInfo }
     * 
     */
    public ProjectBindingInfo createProjectBindingInfo() {
        return new ProjectBindingInfo();
    }

}
