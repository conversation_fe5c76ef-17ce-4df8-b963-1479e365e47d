
package com.neusoft.webserviceinfo.platform.interaction;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;
import com.neusoft.webserviceinfo.platform.bean.ArrayOfProjectBaseInfo;
import com.neusoft.webserviceinfo.platform.bean.ProjectBaseInfo;
import com.neusoft.webserviceinfo.platform.bean.ProjectInfoBean;
import com.neusoft.webserviceinfo.platform.bean.ProjectInfoBeanJSExt;
import com.neusoft.webserviceinfo.platform.bean.ResultInfo;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "ProjectBaseInfoIAPortType", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
@XmlSeeAlso({
    com.neusoft.webserviceinfo.platform.bean.ObjectFactory.class,
    ObjectFactory.class
})
public interface ProjectBaseInfoIAPortType {


    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "projectQueryInfoByKeyCode", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectQueryInfoByKeyCode")
    @ResponseWrapper(localName = "projectQueryInfoByKeyCodeResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectQueryInfoByKeyCodeResponse")
    public String projectQueryInfoByKeyCode(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "projectQueryInfoByTime", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectQueryInfoByTime")
    @ResponseWrapper(localName = "projectQueryInfoByTimeResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectQueryInfoByTimeResponse")
    public String projectQueryInfoByTime(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1,
        @WebParam(name = "in2", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in2);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns com.neusoft.webserviceinfo.platform.bean.ResultInfo
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "checkProjectByCode", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.CheckProjectByCode")
    @ResponseWrapper(localName = "checkProjectByCodeResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.CheckProjectByCodeResponse")
    public ResultInfo checkProjectByCode(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1,
        @WebParam(name = "in2", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns java.lang.String
     */
    @WebMethod(operationName = "QueryProjectAndItemInfoByProCode")
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "QueryProjectAndItemInfoByProCode", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.QueryProjectAndItemInfoByProCode")
    @ResponseWrapper(localName = "QueryProjectAndItemInfoByProCodeResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.QueryProjectAndItemInfoByProCodeResponse")
    public String queryProjectAndItemInfoByProCode(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns com.neusoft.webserviceinfo.platform.bean.ArrayOfProjectBaseInfo
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "projectBaseInfo", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfo")
    @ResponseWrapper(localName = "projectBaseInfoResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoResponse")
    public ArrayOfProjectBaseInfo projectBaseInfo(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns com.neusoft.webserviceinfo.platform.bean.ResultInfo
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "addProjectInfo", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.AddProjectInfo")
    @ResponseWrapper(localName = "addProjectInfoResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.AddProjectInfoResponse")
    public ResultInfo addProjectInfo(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        ProjectInfoBean in1);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @param in4
     * @param in3
     * @return
     *     returns com.neusoft.webserviceinfo.platform.bean.ProjectBaseInfo
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "projectBindingInfo", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectBindingInfo")
    @ResponseWrapper(localName = "projectBindingInfoResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectBindingInfoResponse")
    public ProjectBaseInfo projectBindingInfo(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1,
        @WebParam(name = "in2", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in2,
        @WebParam(name = "in3", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in3,
        @WebParam(name = "in4", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in4);

    /**
     * 
     * @param in0
     * @param in2
     * @param in1
     * @return
     *     returns com.neusoft.webserviceinfo.platform.bean.ResultInfo
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "addProjectInfo1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.AddProjectInfo1")
    @ResponseWrapper(localName = "addProjectInfo1Response", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.AddProjectInfo1Response")
    public ResultInfo addProjectInfo1(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        ProjectInfoBean in1,
        @WebParam(name = "in2", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        ProjectInfoBeanJSExt in2);

    /**
     * 
     * @param in0
     * @param in1
     * @return
     *     returns com.neusoft.webserviceinfo.platform.bean.ProjectBaseInfo
     */
    @WebMethod
    @WebResult(name = "out", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
    @RequestWrapper(localName = "projectQueryInfoByCode", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectQueryInfoByCode")
    @ResponseWrapper(localName = "projectQueryInfoByCodeResponse", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com", className = "com.neusoft.webserviceinfo.platform.interaction.ProjectQueryInfoByCodeResponse")
    public ProjectBaseInfo projectQueryInfoByCode(
        @WebParam(name = "in0", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in0,
        @WebParam(name = "in1", targetNamespace = "http://interaction.platform.webserviceinfo.neusoft.com")
        String in1);

}
