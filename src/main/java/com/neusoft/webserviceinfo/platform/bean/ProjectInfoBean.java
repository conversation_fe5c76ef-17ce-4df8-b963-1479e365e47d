
package com.neusoft.webserviceinfo.platform.bean;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>ProjectInfoBean complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ProjectInfoBean">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="APPLICATION_NOTES" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="APPLY_PROJECT_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="APPLY_TIME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AREA" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AREA_DETIAL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AREA_DETIAL_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="AUDIT_TYPE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CONSTRUCTION_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="CZYC" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DEAL_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DECLAREUNIT_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DECLAREUNIT_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="INDUSTRY" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IS_CERTIFICATION" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IS_DEL" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IS_FOREIGN" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IS_MASSAGE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="IS_PREHEARING" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ITEM_PERSON" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="ITEM_USERNAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="MONEY_IMPLEMENT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OPERATE_TIME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="OPERATOR" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PERSON_CERTNO" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PERSON_CERTTYPE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PRE_OPINION" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PRE_OPINION_BW" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PRE_TIME_BW" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PRE_TIME_PT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECTLOGUUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECTUUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_ADDRESS" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_DEPT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_ENDTIME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_PHASE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_PROPERTY" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_STARTTIME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROJECT_TYPE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROTYPE_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="PROTYPE_NAME" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SCALE_CONTENT" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SOURCE_FLAG" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="SPDW" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="STATE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="TOTAL_MONEY" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="USER_ID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="VERIFICATION_CODE" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="auditType" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb06" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb08" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb10" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb107" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb108" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb109" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb11" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb110" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb111" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb12" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb13" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb14" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb15" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb16" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb17" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb18" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb19" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb20" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb21" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb22" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb24" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb71" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb72" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProjectInfoBean", propOrder = {
    "applicationnotes",
    "applyprojectname",
    "applytime",
    "area",
    "areadetial",
    "areadetialcode",
    "audittype",
    "constructionname",
    "czyc",
    "dealcode",
    "declareunitcode",
    "declareunitname",
    "deptcode",
    "industry",
    "iscertification",
    "isdel",
    "isforeign",
    "ismassage",
    "isprehearing",
    "itemperson",
    "itemusername",
    "moneyimplement",
    "operatetime",
    "operator",
    "personcertno",
    "personcerttype",
    "preopinion",
    "preopinionbw",
    "pretimebw",
    "pretimept",
    "projectloguuid",
    "projectuuid",
    "projectaddress",
    "projectcode",
    "projectdept",
    "projectendtime",
    "projectname",
    "projectphase",
    "projectproperty",
    "projectstarttime",
    "projecttype",
    "protypecode",
    "protypename",
    "scalecontent",
    "sourceflag",
    "spdw",
    "state",
    "totalmoney",
    "userid",
    "verificationcode",
    "auditType",
    "pb01",
    "pb02",
    "pb03",
    "pb04",
    "pb05",
    "pb06",
    "pb07",
    "pb08",
    "pb09",
    "pb10",
    "pb107",
    "pb108",
    "pb109",
    "pb11",
    "pb110",
    "pb111",
    "pb12",
    "pb13",
    "pb14",
    "pb15",
    "pb16",
    "pb17",
    "pb18",
    "pb19",
    "pb20",
    "pb21",
    "pb22",
    "pb24",
    "pb71",
    "pb72"
})
public class ProjectInfoBean {

    @XmlElementRef(name = "APPLICATION_NOTES", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> applicationnotes;
    @XmlElementRef(name = "APPLY_PROJECT_NAME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> applyprojectname;
    @XmlElementRef(name = "APPLY_TIME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> applytime;
    @XmlElementRef(name = "AREA", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> area;
    @XmlElementRef(name = "AREA_DETIAL", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> areadetial;
    @XmlElementRef(name = "AREA_DETIAL_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> areadetialcode;
    @XmlElementRef(name = "AUDIT_TYPE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> audittype;
    @XmlElementRef(name = "CONSTRUCTION_NAME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> constructionname;
    @XmlElementRef(name = "CZYC", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> czyc;
    @XmlElementRef(name = "DEAL_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> dealcode;
    @XmlElementRef(name = "DECLAREUNIT_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> declareunitcode;
    @XmlElementRef(name = "DECLAREUNIT_NAME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> declareunitname;
    @XmlElementRef(name = "DEPT_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> deptcode;
    @XmlElementRef(name = "INDUSTRY", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> industry;
    @XmlElementRef(name = "IS_CERTIFICATION", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> iscertification;
    @XmlElementRef(name = "IS_DEL", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> isdel;
    @XmlElementRef(name = "IS_FOREIGN", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> isforeign;
    @XmlElementRef(name = "IS_MASSAGE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> ismassage;
    @XmlElementRef(name = "IS_PREHEARING", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> isprehearing;
    @XmlElementRef(name = "ITEM_PERSON", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> itemperson;
    @XmlElementRef(name = "ITEM_USERNAME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> itemusername;
    @XmlElementRef(name = "MONEY_IMPLEMENT", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> moneyimplement;
    @XmlElementRef(name = "OPERATE_TIME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> operatetime;
    @XmlElementRef(name = "OPERATOR", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> operator;
    @XmlElementRef(name = "PERSON_CERTNO", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> personcertno;
    @XmlElementRef(name = "PERSON_CERTTYPE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> personcerttype;
    @XmlElementRef(name = "PRE_OPINION", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> preopinion;
    @XmlElementRef(name = "PRE_OPINION_BW", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> preopinionbw;
    @XmlElementRef(name = "PRE_TIME_BW", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pretimebw;
    @XmlElementRef(name = "PRE_TIME_PT", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pretimept;
    @XmlElementRef(name = "PROJECTLOGUUID", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectloguuid;
    @XmlElementRef(name = "PROJECTUUID", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectuuid;
    @XmlElementRef(name = "PROJECT_ADDRESS", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectaddress;
    @XmlElementRef(name = "PROJECT_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectcode;
    @XmlElementRef(name = "PROJECT_DEPT", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectdept;
    @XmlElementRef(name = "PROJECT_ENDTIME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectendtime;
    @XmlElementRef(name = "PROJECT_NAME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectname;
    @XmlElementRef(name = "PROJECT_PHASE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectphase;
    @XmlElementRef(name = "PROJECT_PROPERTY", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectproperty;
    @XmlElementRef(name = "PROJECT_STARTTIME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projectstarttime;
    @XmlElementRef(name = "PROJECT_TYPE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> projecttype;
    @XmlElementRef(name = "PROTYPE_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> protypecode;
    @XmlElementRef(name = "PROTYPE_NAME", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> protypename;
    @XmlElementRef(name = "SCALE_CONTENT", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> scalecontent;
    @XmlElementRef(name = "SOURCE_FLAG", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> sourceflag;
    @XmlElementRef(name = "SPDW", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> spdw;
    @XmlElementRef(name = "STATE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> state;
    @XmlElementRef(name = "TOTAL_MONEY", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> totalmoney;
    @XmlElementRef(name = "USER_ID", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> userid;
    @XmlElementRef(name = "VERIFICATION_CODE", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> verificationcode;
    @XmlElementRef(name = "auditType", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> auditType;
    @XmlElementRef(name = "pb01", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb01;
    @XmlElementRef(name = "pb02", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb02;
    @XmlElementRef(name = "pb03", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb03;
    @XmlElementRef(name = "pb04", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb04;
    @XmlElementRef(name = "pb05", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb05;
    @XmlElementRef(name = "pb06", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb06;
    @XmlElementRef(name = "pb07", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb07;
    @XmlElementRef(name = "pb08", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb08;
    @XmlElementRef(name = "pb09", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb09;
    @XmlElementRef(name = "pb10", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb10;
    @XmlElementRef(name = "pb107", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb107;
    @XmlElementRef(name = "pb108", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb108;
    @XmlElementRef(name = "pb109", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb109;
    @XmlElementRef(name = "pb11", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb11;
    @XmlElementRef(name = "pb110", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb110;
    @XmlElementRef(name = "pb111", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb111;
    @XmlElementRef(name = "pb12", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb12;
    @XmlElementRef(name = "pb13", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb13;
    @XmlElementRef(name = "pb14", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb14;
    @XmlElementRef(name = "pb15", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb15;
    @XmlElementRef(name = "pb16", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb16;
    @XmlElementRef(name = "pb17", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb17;
    @XmlElementRef(name = "pb18", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb18;
    @XmlElementRef(name = "pb19", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb19;
    @XmlElementRef(name = "pb20", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb20;
    @XmlElementRef(name = "pb21", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb21;
    @XmlElementRef(name = "pb22", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb22;
    @XmlElementRef(name = "pb24", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb24;
    @XmlElementRef(name = "pb71", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb71;
    @XmlElementRef(name = "pb72", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb72;

    /**
     * 获取applicationnotes属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAPPLICATIONNOTES() {
        return applicationnotes;
    }

    /**
     * 设置applicationnotes属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAPPLICATIONNOTES(JAXBElement<String> value) {
        this.applicationnotes = value;
    }

    /**
     * 获取applyprojectname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAPPLYPROJECTNAME() {
        return applyprojectname;
    }

    /**
     * 设置applyprojectname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAPPLYPROJECTNAME(JAXBElement<String> value) {
        this.applyprojectname = value;
    }

    /**
     * 获取applytime属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAPPLYTIME() {
        return applytime;
    }

    /**
     * 设置applytime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAPPLYTIME(JAXBElement<String> value) {
        this.applytime = value;
    }

    /**
     * 获取area属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAREA() {
        return area;
    }

    /**
     * 设置area属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAREA(JAXBElement<String> value) {
        this.area = value;
    }

    /**
     * 获取areadetial属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAREADETIAL() {
        return areadetial;
    }

    /**
     * 设置areadetial属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAREADETIAL(JAXBElement<String> value) {
        this.areadetial = value;
    }

    /**
     * 获取areadetialcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAREADETIALCODE() {
        return areadetialcode;
    }

    /**
     * 设置areadetialcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAREADETIALCODE(JAXBElement<String> value) {
        this.areadetialcode = value;
    }

    /**
     * 获取audittype属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAUDITTYPE() {
        return audittype;
    }

    /**
     * 设置audittype属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAUDITTYPE(JAXBElement<String> value) {
        this.audittype = value;
    }

    /**
     * 获取constructionname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCONSTRUCTIONNAME() {
        return constructionname;
    }

    /**
     * 设置constructionname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCONSTRUCTIONNAME(JAXBElement<String> value) {
        this.constructionname = value;
    }

    /**
     * 获取czyc属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getCZYC() {
        return czyc;
    }

    /**
     * 设置czyc属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setCZYC(JAXBElement<String> value) {
        this.czyc = value;
    }

    /**
     * 获取dealcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDEALCODE() {
        return dealcode;
    }

    /**
     * 设置dealcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDEALCODE(JAXBElement<String> value) {
        this.dealcode = value;
    }

    /**
     * 获取declareunitcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDECLAREUNITCODE() {
        return declareunitcode;
    }

    /**
     * 设置declareunitcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDECLAREUNITCODE(JAXBElement<String> value) {
        this.declareunitcode = value;
    }

    /**
     * 获取declareunitname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDECLAREUNITNAME() {
        return declareunitname;
    }

    /**
     * 设置declareunitname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDECLAREUNITNAME(JAXBElement<String> value) {
        this.declareunitname = value;
    }

    /**
     * 获取deptcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getDEPTCODE() {
        return deptcode;
    }

    /**
     * 设置deptcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setDEPTCODE(JAXBElement<String> value) {
        this.deptcode = value;
    }

    /**
     * 获取industry属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getINDUSTRY() {
        return industry;
    }

    /**
     * 设置industry属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setINDUSTRY(JAXBElement<String> value) {
        this.industry = value;
    }

    /**
     * 获取iscertification属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getISCERTIFICATION() {
        return iscertification;
    }

    /**
     * 设置iscertification属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setISCERTIFICATION(JAXBElement<String> value) {
        this.iscertification = value;
    }

    /**
     * 获取isdel属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getISDEL() {
        return isdel;
    }

    /**
     * 设置isdel属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setISDEL(JAXBElement<String> value) {
        this.isdel = value;
    }

    /**
     * 获取isforeign属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getISFOREIGN() {
        return isforeign;
    }

    /**
     * 设置isforeign属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setISFOREIGN(JAXBElement<String> value) {
        this.isforeign = value;
    }

    /**
     * 获取ismassage属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getISMASSAGE() {
        return ismassage;
    }

    /**
     * 设置ismassage属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setISMASSAGE(JAXBElement<String> value) {
        this.ismassage = value;
    }

    /**
     * 获取isprehearing属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getISPREHEARING() {
        return isprehearing;
    }

    /**
     * 设置isprehearing属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setISPREHEARING(JAXBElement<String> value) {
        this.isprehearing = value;
    }

    /**
     * 获取itemperson属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getITEMPERSON() {
        return itemperson;
    }

    /**
     * 设置itemperson属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setITEMPERSON(JAXBElement<String> value) {
        this.itemperson = value;
    }

    /**
     * 获取itemusername属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getITEMUSERNAME() {
        return itemusername;
    }

    /**
     * 设置itemusername属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setITEMUSERNAME(JAXBElement<String> value) {
        this.itemusername = value;
    }

    /**
     * 获取moneyimplement属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getMONEYIMPLEMENT() {
        return moneyimplement;
    }

    /**
     * 设置moneyimplement属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setMONEYIMPLEMENT(JAXBElement<String> value) {
        this.moneyimplement = value;
    }

    /**
     * 获取operatetime属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getOPERATETIME() {
        return operatetime;
    }

    /**
     * 设置operatetime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setOPERATETIME(JAXBElement<String> value) {
        this.operatetime = value;
    }

    /**
     * 获取operator属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getOPERATOR() {
        return operator;
    }

    /**
     * 设置operator属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setOPERATOR(JAXBElement<String> value) {
        this.operator = value;
    }

    /**
     * 获取personcertno属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPERSONCERTNO() {
        return personcertno;
    }

    /**
     * 设置personcertno属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPERSONCERTNO(JAXBElement<String> value) {
        this.personcertno = value;
    }

    /**
     * 获取personcerttype属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPERSONCERTTYPE() {
        return personcerttype;
    }

    /**
     * 设置personcerttype属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPERSONCERTTYPE(JAXBElement<String> value) {
        this.personcerttype = value;
    }

    /**
     * 获取preopinion属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPREOPINION() {
        return preopinion;
    }

    /**
     * 设置preopinion属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPREOPINION(JAXBElement<String> value) {
        this.preopinion = value;
    }

    /**
     * 获取preopinionbw属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPREOPINIONBW() {
        return preopinionbw;
    }

    /**
     * 设置preopinionbw属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPREOPINIONBW(JAXBElement<String> value) {
        this.preopinionbw = value;
    }

    /**
     * 获取pretimebw属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPRETIMEBW() {
        return pretimebw;
    }

    /**
     * 设置pretimebw属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPRETIMEBW(JAXBElement<String> value) {
        this.pretimebw = value;
    }

    /**
     * 获取pretimept属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPRETIMEPT() {
        return pretimept;
    }

    /**
     * 设置pretimept属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPRETIMEPT(JAXBElement<String> value) {
        this.pretimept = value;
    }

    /**
     * 获取projectloguuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTLOGUUID() {
        return projectloguuid;
    }

    /**
     * 设置projectloguuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTLOGUUID(JAXBElement<String> value) {
        this.projectloguuid = value;
    }

    /**
     * 获取projectuuid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTUUID() {
        return projectuuid;
    }

    /**
     * 设置projectuuid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTUUID(JAXBElement<String> value) {
        this.projectuuid = value;
    }

    /**
     * 获取projectaddress属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTADDRESS() {
        return projectaddress;
    }

    /**
     * 设置projectaddress属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTADDRESS(JAXBElement<String> value) {
        this.projectaddress = value;
    }

    /**
     * 获取projectcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTCODE() {
        return projectcode;
    }

    /**
     * 设置projectcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTCODE(JAXBElement<String> value) {
        this.projectcode = value;
    }

    /**
     * 获取projectdept属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTDEPT() {
        return projectdept;
    }

    /**
     * 设置projectdept属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTDEPT(JAXBElement<String> value) {
        this.projectdept = value;
    }

    /**
     * 获取projectendtime属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTENDTIME() {
        return projectendtime;
    }

    /**
     * 设置projectendtime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTENDTIME(JAXBElement<String> value) {
        this.projectendtime = value;
    }

    /**
     * 获取projectname属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTNAME() {
        return projectname;
    }

    /**
     * 设置projectname属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTNAME(JAXBElement<String> value) {
        this.projectname = value;
    }

    /**
     * 获取projectphase属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTPHASE() {
        return projectphase;
    }

    /**
     * 设置projectphase属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTPHASE(JAXBElement<String> value) {
        this.projectphase = value;
    }

    /**
     * 获取projectproperty属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTPROPERTY() {
        return projectproperty;
    }

    /**
     * 设置projectproperty属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTPROPERTY(JAXBElement<String> value) {
        this.projectproperty = value;
    }

    /**
     * 获取projectstarttime属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTSTARTTIME() {
        return projectstarttime;
    }

    /**
     * 设置projectstarttime属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTSTARTTIME(JAXBElement<String> value) {
        this.projectstarttime = value;
    }

    /**
     * 获取projecttype属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROJECTTYPE() {
        return projecttype;
    }

    /**
     * 设置projecttype属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROJECTTYPE(JAXBElement<String> value) {
        this.projecttype = value;
    }

    /**
     * 获取protypecode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROTYPECODE() {
        return protypecode;
    }

    /**
     * 设置protypecode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROTYPECODE(JAXBElement<String> value) {
        this.protypecode = value;
    }

    /**
     * 获取protypename属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPROTYPENAME() {
        return protypename;
    }

    /**
     * 设置protypename属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPROTYPENAME(JAXBElement<String> value) {
        this.protypename = value;
    }

    /**
     * 获取scalecontent属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSCALECONTENT() {
        return scalecontent;
    }

    /**
     * 设置scalecontent属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSCALECONTENT(JAXBElement<String> value) {
        this.scalecontent = value;
    }

    /**
     * 获取sourceflag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSOURCEFLAG() {
        return sourceflag;
    }

    /**
     * 设置sourceflag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSOURCEFLAG(JAXBElement<String> value) {
        this.sourceflag = value;
    }

    /**
     * 获取spdw属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSPDW() {
        return spdw;
    }

    /**
     * 设置spdw属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSPDW(JAXBElement<String> value) {
        this.spdw = value;
    }

    /**
     * 获取state属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getSTATE() {
        return state;
    }

    /**
     * 设置state属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setSTATE(JAXBElement<String> value) {
        this.state = value;
    }

    /**
     * 获取totalmoney属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getTOTALMONEY() {
        return totalmoney;
    }

    /**
     * 设置totalmoney属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setTOTALMONEY(JAXBElement<String> value) {
        this.totalmoney = value;
    }

    /**
     * 获取userid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getUSERID() {
        return userid;
    }

    /**
     * 设置userid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setUSERID(JAXBElement<String> value) {
        this.userid = value;
    }

    /**
     * 获取verificationcode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getVERIFICATIONCODE() {
        return verificationcode;
    }

    /**
     * 设置verificationcode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setVERIFICATIONCODE(JAXBElement<String> value) {
        this.verificationcode = value;
    }

    /**
     * 获取auditType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getAuditType() {
        return auditType;
    }

    /**
     * 设置auditType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setAuditType(JAXBElement<String> value) {
        this.auditType = value;
    }

    /**
     * 获取pb01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb01() {
        return pb01;
    }

    /**
     * 设置pb01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb01(JAXBElement<String> value) {
        this.pb01 = value;
    }

    /**
     * 获取pb02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb02() {
        return pb02;
    }

    /**
     * 设置pb02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb02(JAXBElement<String> value) {
        this.pb02 = value;
    }

    /**
     * 获取pb03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb03() {
        return pb03;
    }

    /**
     * 设置pb03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb03(JAXBElement<String> value) {
        this.pb03 = value;
    }

    /**
     * 获取pb04属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb04() {
        return pb04;
    }

    /**
     * 设置pb04属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb04(JAXBElement<String> value) {
        this.pb04 = value;
    }

    /**
     * 获取pb05属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb05() {
        return pb05;
    }

    /**
     * 设置pb05属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb05(JAXBElement<String> value) {
        this.pb05 = value;
    }

    /**
     * 获取pb06属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb06() {
        return pb06;
    }

    /**
     * 设置pb06属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb06(JAXBElement<String> value) {
        this.pb06 = value;
    }

    /**
     * 获取pb07属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb07() {
        return pb07;
    }

    /**
     * 设置pb07属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb07(JAXBElement<String> value) {
        this.pb07 = value;
    }

    /**
     * 获取pb08属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb08() {
        return pb08;
    }

    /**
     * 设置pb08属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb08(JAXBElement<String> value) {
        this.pb08 = value;
    }

    /**
     * 获取pb09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb09() {
        return pb09;
    }

    /**
     * 设置pb09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb09(JAXBElement<String> value) {
        this.pb09 = value;
    }

    /**
     * 获取pb10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb10() {
        return pb10;
    }

    /**
     * 设置pb10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb10(JAXBElement<String> value) {
        this.pb10 = value;
    }

    /**
     * 获取pb107属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb107() {
        return pb107;
    }

    /**
     * 设置pb107属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb107(JAXBElement<String> value) {
        this.pb107 = value;
    }

    /**
     * 获取pb108属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb108() {
        return pb108;
    }

    /**
     * 设置pb108属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb108(JAXBElement<String> value) {
        this.pb108 = value;
    }

    /**
     * 获取pb109属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb109() {
        return pb109;
    }

    /**
     * 设置pb109属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb109(JAXBElement<String> value) {
        this.pb109 = value;
    }

    /**
     * 获取pb11属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb11() {
        return pb11;
    }

    /**
     * 设置pb11属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb11(JAXBElement<String> value) {
        this.pb11 = value;
    }

    /**
     * 获取pb110属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb110() {
        return pb110;
    }

    /**
     * 设置pb110属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb110(JAXBElement<String> value) {
        this.pb110 = value;
    }

    /**
     * 获取pb111属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb111() {
        return pb111;
    }

    /**
     * 设置pb111属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb111(JAXBElement<String> value) {
        this.pb111 = value;
    }

    /**
     * 获取pb12属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb12() {
        return pb12;
    }

    /**
     * 设置pb12属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb12(JAXBElement<String> value) {
        this.pb12 = value;
    }

    /**
     * 获取pb13属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb13() {
        return pb13;
    }

    /**
     * 设置pb13属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb13(JAXBElement<String> value) {
        this.pb13 = value;
    }

    /**
     * 获取pb14属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb14() {
        return pb14;
    }

    /**
     * 设置pb14属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb14(JAXBElement<String> value) {
        this.pb14 = value;
    }

    /**
     * 获取pb15属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb15() {
        return pb15;
    }

    /**
     * 设置pb15属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb15(JAXBElement<String> value) {
        this.pb15 = value;
    }

    /**
     * 获取pb16属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb16() {
        return pb16;
    }

    /**
     * 设置pb16属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb16(JAXBElement<String> value) {
        this.pb16 = value;
    }

    /**
     * 获取pb17属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb17() {
        return pb17;
    }

    /**
     * 设置pb17属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb17(JAXBElement<String> value) {
        this.pb17 = value;
    }

    /**
     * 获取pb18属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb18() {
        return pb18;
    }

    /**
     * 设置pb18属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb18(JAXBElement<String> value) {
        this.pb18 = value;
    }

    /**
     * 获取pb19属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb19() {
        return pb19;
    }

    /**
     * 设置pb19属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb19(JAXBElement<String> value) {
        this.pb19 = value;
    }

    /**
     * 获取pb20属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb20() {
        return pb20;
    }

    /**
     * 设置pb20属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb20(JAXBElement<String> value) {
        this.pb20 = value;
    }

    /**
     * 获取pb21属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb21() {
        return pb21;
    }

    /**
     * 设置pb21属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb21(JAXBElement<String> value) {
        this.pb21 = value;
    }

    /**
     * 获取pb22属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb22() {
        return pb22;
    }

    /**
     * 设置pb22属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb22(JAXBElement<String> value) {
        this.pb22 = value;
    }

    /**
     * 获取pb24属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb24() {
        return pb24;
    }

    /**
     * 设置pb24属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb24(JAXBElement<String> value) {
        this.pb24 = value;
    }

    /**
     * 获取pb71属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb71() {
        return pb71;
    }

    /**
     * 设置pb71属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb71(JAXBElement<String> value) {
        this.pb71 = value;
    }

    /**
     * 获取pb72属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb72() {
        return pb72;
    }

    /**
     * 设置pb72属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb72(JAXBElement<String> value) {
        this.pb72 = value;
    }

}
