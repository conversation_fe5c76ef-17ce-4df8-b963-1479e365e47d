
package com.neusoft.webserviceinfo.platform.bean;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.neusoft.webserviceinfo.platform.bean package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _ProjectInfoBeanJSExtPb70_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb70");
    private final static QName _ProjectInfoBeanJSExtPb81_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb81");
    private final static QName _ProjectInfoBeanJSExtPb80_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb80");
    private final static QName _ProjectInfoBeanJSExtPb74_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb74");
    private final static QName _ProjectInfoBeanJSExtPb85_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb85");
    private final static QName _ProjectInfoBeanJSExtPb73_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb73");
    private final static QName _ProjectInfoBeanJSExtPb84_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb84");
    private final static QName _ProjectInfoBeanJSExtPb72_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb72");
    private final static QName _ProjectInfoBeanJSExtPb83_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb83");
    private final static QName _ProjectInfoBeanJSExtPb71_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb71");
    private final static QName _ProjectInfoBeanJSExtPb82_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb82");
    private final static QName _ProjectInfoBeanJSExtPb01_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb01");
    private final static QName _ProjectInfoBeanJSExtPb78_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb78");
    private final static QName _ProjectInfoBeanJSExtPb77_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb77");
    private final static QName _ProjectInfoBeanJSExtPb76_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb76");
    private final static QName _ProjectInfoBeanJSExtPb75_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb75");
    private final static QName _ProjectInfoBeanJSExtPb79_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb79");
    private final static QName _ProjectInfoBeanDEALCODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "DEAL_CODE");
    private final static QName _ProjectInfoBeanPROJECTSTARTTIME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_STARTTIME");
    private final static QName _ProjectInfoBeanPROTYPENAME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROTYPE_NAME");
    private final static QName _ProjectInfoBeanPRETIMEBW_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PRE_TIME_BW");
    private final static QName _ProjectInfoBeanAUDITTYPE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "AUDIT_TYPE");
    private final static QName _ProjectInfoBeanPb19_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb19");
    private final static QName _ProjectInfoBeanPROJECTPHASE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_PHASE");
    private final static QName _ProjectInfoBeanPb18_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb18");
    private final static QName _ProjectInfoBeanPb17_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb17");
    private final static QName _ProjectInfoBeanPROJECTCODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_CODE");
    private final static QName _ProjectInfoBeanAREADETIALCODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "AREA_DETIAL_CODE");
    private final static QName _ProjectInfoBeanPb12_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb12");
    private final static QName _ProjectInfoBeanPROJECTADDRESS_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_ADDRESS");
    private final static QName _ProjectInfoBeanPb11_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb11");
    private final static QName _ProjectInfoBeanDEPTCODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "DEPT_CODE");
    private final static QName _ProjectInfoBeanPb10_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb10");
    private final static QName _ProjectInfoBeanPb16_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb16");
    private final static QName _ProjectInfoBeanPROJECTTYPE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_TYPE");
    private final static QName _ProjectInfoBeanPb15_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb15");
    private final static QName _ProjectInfoBeanPb14_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb14");
    private final static QName _ProjectInfoBeanPb13_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb13");
    private final static QName _ProjectInfoBeanAPPLYTIME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "APPLY_TIME");
    private final static QName _ProjectInfoBeanPb109_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb109");
    private final static QName _ProjectInfoBeanPREOPINION_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PRE_OPINION");
    private final static QName _ProjectInfoBeanVERIFICATIONCODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "VERIFICATION_CODE");
    private final static QName _ProjectInfoBeanISCERTIFICATION_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "IS_CERTIFICATION");
    private final static QName _ProjectInfoBeanISDEL_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "IS_DEL");
    private final static QName _ProjectInfoBeanSCALECONTENT_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "SCALE_CONTENT");
    private final static QName _ProjectInfoBeanDECLAREUNITCODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "DECLAREUNIT_CODE");
    private final static QName _ProjectInfoBeanSPDW_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "SPDW");
    private final static QName _ProjectInfoBeanITEMUSERNAME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "ITEM_USERNAME");
    private final static QName _ProjectInfoBeanSOURCEFLAG_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "SOURCE_FLAG");
    private final static QName _ProjectInfoBeanAREA_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "AREA");
    private final static QName _ProjectInfoBeanOPERATETIME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "OPERATE_TIME");
    private final static QName _ProjectInfoBeanPb107_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb107");
    private final static QName _ProjectInfoBeanAPPLYPROJECTNAME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "APPLY_PROJECT_NAME");
    private final static QName _ProjectInfoBeanPROJECTDEPT_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_DEPT");
    private final static QName _ProjectInfoBeanPROJECTENDTIME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_ENDTIME");
    private final static QName _ProjectInfoBeanPb108_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb108");
    private final static QName _ProjectInfoBeanAPPLICATIONNOTES_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "APPLICATION_NOTES");
    private final static QName _ProjectInfoBeanUSERID_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "USER_ID");
    private final static QName _ProjectInfoBeanPb110_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb110");
    private final static QName _ProjectInfoBeanISFOREIGN_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "IS_FOREIGN");
    private final static QName _ProjectInfoBeanPb111_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb111");
    private final static QName _ProjectInfoBeanPROJECTPROPERTY_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_PROPERTY");
    private final static QName _ProjectInfoBeanPb22_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb22");
    private final static QName _ProjectInfoBeanPb21_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb21");
    private final static QName _ProjectInfoBeanPb20_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb20");
    private final static QName _ProjectInfoBeanPRETIMEPT_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PRE_TIME_PT");
    private final static QName _ProjectInfoBeanPb24_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb24");
    private final static QName _ProjectInfoBeanISPREHEARING_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "IS_PREHEARING");
    private final static QName _ProjectInfoBeanPERSONCERTTYPE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PERSON_CERTTYPE");
    private final static QName _ProjectInfoBeanMONEYIMPLEMENT_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "MONEY_IMPLEMENT");
    private final static QName _ProjectInfoBeanOPERATOR_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "OPERATOR");
    private final static QName _ProjectInfoBeanDECLAREUNITNAME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "DECLAREUNIT_NAME");
    private final static QName _ProjectInfoBeanPREOPINIONBW_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PRE_OPINION_BW");
    private final static QName _ProjectInfoBeanAREADETIAL_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "AREA_DETIAL");
    private final static QName _ProjectInfoBeanPROJECTUUID_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECTUUID");
    private final static QName _ProjectInfoBeanTOTALMONEY_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "TOTAL_MONEY");
    private final static QName _ProjectInfoBeanINDUSTRY_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "INDUSTRY");
    private final static QName _ProjectInfoBeanITEMPERSON_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "ITEM_PERSON");
    private final static QName _ProjectInfoBeanCZYC_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "CZYC");
    private final static QName _ProjectInfoBeanPERSONCERTNO_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PERSON_CERTNO");
    private final static QName _ProjectInfoBeanPROTYPECODE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROTYPE_CODE");
    private final static QName _ProjectInfoBeanPb09_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb09");
    private final static QName _ProjectInfoBeanPROJECTNAME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_NAME");
    private final static QName _ProjectInfoBeanPb08_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb08");
    private final static QName _ProjectInfoBeanPb07_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb07");
    private final static QName _ProjectInfoBeanPb06_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb06");
    private final static QName _ProjectInfoBeanAuditType_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "auditType");
    private final static QName _ProjectInfoBeanSTATE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "STATE");
    private final static QName _ProjectInfoBeanPb05_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb05");
    private final static QName _ProjectInfoBeanISMASSAGE_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "IS_MASSAGE");
    private final static QName _ProjectInfoBeanPROJECTLOGUUID_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECTLOGUUID");
    private final static QName _ProjectInfoBeanPb04_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb04");
    private final static QName _ProjectInfoBeanCONSTRUCTIONNAME_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "CONSTRUCTION_NAME");
    private final static QName _ProjectInfoBeanPb03_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb03");
    private final static QName _ProjectInfoBeanPb02_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb02");
    private final static QName _ProjectBaseInfoPb26_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb26");
    private final static QName _ProjectBaseInfoPb36_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb36");
    private final static QName _ResultInfoPm02_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pm02");
    private final static QName _ResultInfoPm01_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pm01");
    private final static QName _ResultInfoPm03_QNAME = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pm03");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.neusoft.webserviceinfo.platform.bean
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ResultInfo }
     * 
     */
    public ResultInfo createResultInfo() {
        return new ResultInfo();
    }

    /**
     * Create an instance of {@link ArrayOfProjectBaseInfo }
     * 
     */
    public ArrayOfProjectBaseInfo createArrayOfProjectBaseInfo() {
        return new ArrayOfProjectBaseInfo();
    }

    /**
     * Create an instance of {@link ProjectBaseInfo }
     * 
     */
    public ProjectBaseInfo createProjectBaseInfo() {
        return new ProjectBaseInfo();
    }

    /**
     * Create an instance of {@link ProjectInfoBean }
     * 
     */
    public ProjectInfoBean createProjectInfoBean() {
        return new ProjectInfoBean();
    }

    /**
     * Create an instance of {@link ProjectInfoBeanJSExt }
     * 
     */
    public ProjectInfoBeanJSExt createProjectInfoBeanJSExt() {
        return new ProjectInfoBeanJSExt();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb70", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb70(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb70_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb81", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb81(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb81_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb80", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb80(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb80_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb74", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb74(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb74_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb85", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb85(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb85_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb73", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb73(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb73_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb84", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb84(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb84_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb72", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb72(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb72_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb83", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb83(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb83_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb71", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb71(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb71_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb82", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb82(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb82_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb01", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb01(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb01_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb78", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb78(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb78_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb77", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb77(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb77_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb76", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb76(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb76_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb75", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb75(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb75_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb79", scope = ProjectInfoBeanJSExt.class)
    public JAXBElement<String> createProjectInfoBeanJSExtPb79(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb79_QNAME, String.class, ProjectInfoBeanJSExt.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "DEAL_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanDEALCODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanDEALCODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_STARTTIME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTSTARTTIME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTSTARTTIME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROTYPE_NAME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROTYPENAME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROTYPENAME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PRE_TIME_BW", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPRETIMEBW(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPRETIMEBW_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "AUDIT_TYPE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAUDITTYPE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAUDITTYPE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb19", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb19(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb19_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_PHASE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTPHASE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTPHASE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb18", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb18(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb18_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb17", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb17(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb17_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTCODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTCODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "AREA_DETIAL_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAREADETIALCODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAREADETIALCODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb12", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb12(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb12_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_ADDRESS", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTADDRESS(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTADDRESS_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb11", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb11(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb11_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "DEPT_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanDEPTCODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanDEPTCODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb10", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb10(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb10_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb16", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb16(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb16_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_TYPE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTTYPE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTTYPE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb15", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb15(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb15_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb14", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb14(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb14_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb13", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb13(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb13_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "APPLY_TIME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAPPLYTIME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAPPLYTIME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb109", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb109(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb109_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PRE_OPINION", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPREOPINION(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPREOPINION_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "VERIFICATION_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanVERIFICATIONCODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanVERIFICATIONCODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "IS_CERTIFICATION", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanISCERTIFICATION(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanISCERTIFICATION_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "IS_DEL", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanISDEL(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanISDEL_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "SCALE_CONTENT", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanSCALECONTENT(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanSCALECONTENT_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "DECLAREUNIT_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanDECLAREUNITCODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanDECLAREUNITCODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "SPDW", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanSPDW(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanSPDW_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "ITEM_USERNAME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanITEMUSERNAME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanITEMUSERNAME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "SOURCE_FLAG", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanSOURCEFLAG(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanSOURCEFLAG_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "AREA", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAREA(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAREA_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "OPERATE_TIME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanOPERATETIME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanOPERATETIME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb107", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb107(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb107_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "APPLY_PROJECT_NAME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAPPLYPROJECTNAME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAPPLYPROJECTNAME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_DEPT", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTDEPT(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTDEPT_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_ENDTIME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTENDTIME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTENDTIME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb108", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb108(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb108_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "APPLICATION_NOTES", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAPPLICATIONNOTES(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAPPLICATIONNOTES_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "USER_ID", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanUSERID(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanUSERID_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb110", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb110(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb110_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "IS_FOREIGN", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanISFOREIGN(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanISFOREIGN_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb111", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb111(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb111_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_PROPERTY", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTPROPERTY(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTPROPERTY_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb22", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb22(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb22_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb21", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb21(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb21_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb20", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb20(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb20_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PRE_TIME_PT", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPRETIMEPT(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPRETIMEPT_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb24", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb24(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb24_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "IS_PREHEARING", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanISPREHEARING(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanISPREHEARING_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PERSON_CERTTYPE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPERSONCERTTYPE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPERSONCERTTYPE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb72", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb72(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb72_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb71", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb71(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb71_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "MONEY_IMPLEMENT", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanMONEYIMPLEMENT(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanMONEYIMPLEMENT_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "OPERATOR", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanOPERATOR(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanOPERATOR_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "DECLAREUNIT_NAME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanDECLAREUNITNAME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanDECLAREUNITNAME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PRE_OPINION_BW", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPREOPINIONBW(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPREOPINIONBW_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "AREA_DETIAL", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAREADETIAL(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAREADETIAL_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECTUUID", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTUUID(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTUUID_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "TOTAL_MONEY", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanTOTALMONEY(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanTOTALMONEY_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "INDUSTRY", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanINDUSTRY(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanINDUSTRY_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "ITEM_PERSON", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanITEMPERSON(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanITEMPERSON_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "CZYC", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanCZYC(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanCZYC_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PERSON_CERTNO", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPERSONCERTNO(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPERSONCERTNO_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROTYPE_CODE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROTYPECODE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROTYPECODE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb09", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb09(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb09_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECT_NAME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTNAME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTNAME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb08", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb08(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb08_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb07", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb07(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb07_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb06", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb06(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb06_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "auditType", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanAuditType(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanAuditType_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "STATE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanSTATE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanSTATE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb01", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb01(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb01_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb05", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb05(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb05_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "IS_MASSAGE", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanISMASSAGE(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanISMASSAGE_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "PROJECTLOGUUID", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPROJECTLOGUUID(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPROJECTLOGUUID_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb04", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb04(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb04_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "CONSTRUCTION_NAME", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanCONSTRUCTIONNAME(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanCONSTRUCTIONNAME_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb03", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb03(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb03_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb02", scope = ProjectInfoBean.class)
    public JAXBElement<String> createProjectInfoBeanPb02(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb02_QNAME, String.class, ProjectInfoBean.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb09", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb09(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb09_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb19", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb19(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb19_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb07", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb07(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb07_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb18", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb18(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb18_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb17", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb17(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb17_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb01", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb01(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanJSExtPb01_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb12", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb12(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb12_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb11", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb11(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb11_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb10", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb10(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb10_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb21", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb21(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb21_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb20", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb20(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb20_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb05", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb05(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb05_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb16", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb16(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb16_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb04", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb04(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb04_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb15", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb15(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb15_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb26", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb26(String value) {
        return new JAXBElement<String>(_ProjectBaseInfoPb26_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb03", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb03(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb03_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb14", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb14(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb14_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb36", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb36(String value) {
        return new JAXBElement<String>(_ProjectBaseInfoPb36_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb02", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb02(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb02_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pb13", scope = ProjectBaseInfo.class)
    public JAXBElement<String> createProjectBaseInfoPb13(String value) {
        return new JAXBElement<String>(_ProjectInfoBeanPb13_QNAME, String.class, ProjectBaseInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pm02", scope = ResultInfo.class)
    public JAXBElement<String> createResultInfoPm02(String value) {
        return new JAXBElement<String>(_ResultInfoPm02_QNAME, String.class, ResultInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pm01", scope = ResultInfo.class)
    public JAXBElement<String> createResultInfoPm01(String value) {
        return new JAXBElement<String>(_ResultInfoPm01_QNAME, String.class, ResultInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://bean.platform.webserviceinfo.neusoft.com", name = "pm03", scope = ResultInfo.class)
    public JAXBElement<String> createResultInfoPm03(String value) {
        return new JAXBElement<String>(_ResultInfoPm03_QNAME, String.class, ResultInfo.class, value);
    }

}
