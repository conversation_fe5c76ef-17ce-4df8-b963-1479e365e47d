
package com.neusoft.webserviceinfo.platform.bean;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>ResultInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ResultInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pm01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pm02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pm03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ResultInfo", propOrder = {
    "pm01",
    "pm02",
    "pm03"
})
public class ResultInfo {

    @XmlElementRef(name = "pm01", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pm01;
    @XmlElementRef(name = "pm02", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pm02;
    @XmlElementRef(name = "pm03", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pm03;

    /**
     * 获取pm01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPm01() {
        return pm01;
    }

    /**
     * 设置pm01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPm01(JAXBElement<String> value) {
        this.pm01 = value;
    }

    /**
     * 获取pm02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPm02() {
        return pm02;
    }

    /**
     * 设置pm02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPm02(JAXBElement<String> value) {
        this.pm02 = value;
    }

    /**
     * 获取pm03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPm03() {
        return pm03;
    }

    /**
     * 设置pm03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPm03(JAXBElement<String> value) {
        this.pm03 = value;
    }

}
