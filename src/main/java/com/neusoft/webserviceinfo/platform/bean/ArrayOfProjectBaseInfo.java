
package com.neusoft.webserviceinfo.platform.bean;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>ArrayOfProjectBaseInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ArrayOfProjectBaseInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ProjectBaseInfo" type="{http://bean.platform.webserviceinfo.neusoft.com}ProjectBaseInfo" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ArrayOfProjectBaseInfo", propOrder = {
    "projectBaseInfo"
})
public class ArrayOfProjectBaseInfo {

    @XmlElement(name = "ProjectBaseInfo", nillable = true)
    protected List<ProjectBaseInfo> projectBaseInfo;

    /**
     * Gets the value of the projectBaseInfo property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the projectBaseInfo property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getProjectBaseInfo().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ProjectBaseInfo }
     * 
     * 
     */
    public List<ProjectBaseInfo> getProjectBaseInfo() {
        if (projectBaseInfo == null) {
            projectBaseInfo = new ArrayList<ProjectBaseInfo>();
        }
        return this.projectBaseInfo;
    }

}
