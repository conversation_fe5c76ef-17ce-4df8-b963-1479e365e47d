
package com.neusoft.webserviceinfo.platform.bean;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>ProjectBaseInfo complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ProjectBaseInfo">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pb01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb02" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb03" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb04" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb05" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb07" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb09" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb10" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb11" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb12" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb13" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb14" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb15" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb16" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb17" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb18" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb19" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb20" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb21" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb26" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb36" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProjectBaseInfo", propOrder = {
    "pb01",
    "pb02",
    "pb03",
    "pb04",
    "pb05",
    "pb07",
    "pb09",
    "pb10",
    "pb11",
    "pb12",
    "pb13",
    "pb14",
    "pb15",
    "pb16",
    "pb17",
    "pb18",
    "pb19",
    "pb20",
    "pb21",
    "pb26",
    "pb36"
})
public class ProjectBaseInfo {

    @XmlElementRef(name = "pb01", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb01;
    @XmlElementRef(name = "pb02", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb02;
    @XmlElementRef(name = "pb03", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb03;
    @XmlElementRef(name = "pb04", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb04;
    @XmlElementRef(name = "pb05", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb05;
    @XmlElementRef(name = "pb07", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb07;
    @XmlElementRef(name = "pb09", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb09;
    @XmlElementRef(name = "pb10", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb10;
    @XmlElementRef(name = "pb11", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb11;
    @XmlElementRef(name = "pb12", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb12;
    @XmlElementRef(name = "pb13", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb13;
    @XmlElementRef(name = "pb14", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb14;
    @XmlElementRef(name = "pb15", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb15;
    @XmlElementRef(name = "pb16", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb16;
    @XmlElementRef(name = "pb17", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb17;
    @XmlElementRef(name = "pb18", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb18;
    @XmlElementRef(name = "pb19", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb19;
    @XmlElementRef(name = "pb20", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb20;
    @XmlElementRef(name = "pb21", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb21;
    @XmlElementRef(name = "pb26", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb26;
    @XmlElementRef(name = "pb36", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb36;

    /**
     * 获取pb01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb01() {
        return pb01;
    }

    /**
     * 设置pb01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb01(JAXBElement<String> value) {
        this.pb01 = value;
    }

    /**
     * 获取pb02属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb02() {
        return pb02;
    }

    /**
     * 设置pb02属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb02(JAXBElement<String> value) {
        this.pb02 = value;
    }

    /**
     * 获取pb03属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb03() {
        return pb03;
    }

    /**
     * 设置pb03属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb03(JAXBElement<String> value) {
        this.pb03 = value;
    }

    /**
     * 获取pb04属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb04() {
        return pb04;
    }

    /**
     * 设置pb04属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb04(JAXBElement<String> value) {
        this.pb04 = value;
    }

    /**
     * 获取pb05属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb05() {
        return pb05;
    }

    /**
     * 设置pb05属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb05(JAXBElement<String> value) {
        this.pb05 = value;
    }

    /**
     * 获取pb07属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb07() {
        return pb07;
    }

    /**
     * 设置pb07属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb07(JAXBElement<String> value) {
        this.pb07 = value;
    }

    /**
     * 获取pb09属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb09() {
        return pb09;
    }

    /**
     * 设置pb09属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb09(JAXBElement<String> value) {
        this.pb09 = value;
    }

    /**
     * 获取pb10属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb10() {
        return pb10;
    }

    /**
     * 设置pb10属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb10(JAXBElement<String> value) {
        this.pb10 = value;
    }

    /**
     * 获取pb11属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb11() {
        return pb11;
    }

    /**
     * 设置pb11属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb11(JAXBElement<String> value) {
        this.pb11 = value;
    }

    /**
     * 获取pb12属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb12() {
        return pb12;
    }

    /**
     * 设置pb12属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb12(JAXBElement<String> value) {
        this.pb12 = value;
    }

    /**
     * 获取pb13属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb13() {
        return pb13;
    }

    /**
     * 设置pb13属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb13(JAXBElement<String> value) {
        this.pb13 = value;
    }

    /**
     * 获取pb14属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb14() {
        return pb14;
    }

    /**
     * 设置pb14属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb14(JAXBElement<String> value) {
        this.pb14 = value;
    }

    /**
     * 获取pb15属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb15() {
        return pb15;
    }

    /**
     * 设置pb15属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb15(JAXBElement<String> value) {
        this.pb15 = value;
    }

    /**
     * 获取pb16属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb16() {
        return pb16;
    }

    /**
     * 设置pb16属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb16(JAXBElement<String> value) {
        this.pb16 = value;
    }

    /**
     * 获取pb17属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb17() {
        return pb17;
    }

    /**
     * 设置pb17属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb17(JAXBElement<String> value) {
        this.pb17 = value;
    }

    /**
     * 获取pb18属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb18() {
        return pb18;
    }

    /**
     * 设置pb18属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb18(JAXBElement<String> value) {
        this.pb18 = value;
    }

    /**
     * 获取pb19属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb19() {
        return pb19;
    }

    /**
     * 设置pb19属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb19(JAXBElement<String> value) {
        this.pb19 = value;
    }

    /**
     * 获取pb20属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb20() {
        return pb20;
    }

    /**
     * 设置pb20属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb20(JAXBElement<String> value) {
        this.pb20 = value;
    }

    /**
     * 获取pb21属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb21() {
        return pb21;
    }

    /**
     * 设置pb21属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb21(JAXBElement<String> value) {
        this.pb21 = value;
    }

    /**
     * 获取pb26属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb26() {
        return pb26;
    }

    /**
     * 设置pb26属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb26(JAXBElement<String> value) {
        this.pb26 = value;
    }

    /**
     * 获取pb36属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb36() {
        return pb36;
    }

    /**
     * 设置pb36属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb36(JAXBElement<String> value) {
        this.pb36 = value;
    }

}
