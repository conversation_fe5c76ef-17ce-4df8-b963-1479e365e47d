
package com.neusoft.webserviceinfo.platform.bean;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>ProjectInfoBeanJSExt complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="ProjectInfoBeanJSExt">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pb01" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb70" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb71" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb72" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb73" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb74" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb75" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb76" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb77" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb78" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb79" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb80" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb81" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb82" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb83" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb84" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *         &lt;element name="pb85" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProjectInfoBeanJSExt", propOrder = {
    "pb01",
    "pb70",
    "pb71",
    "pb72",
    "pb73",
    "pb74",
    "pb75",
    "pb76",
    "pb77",
    "pb78",
    "pb79",
    "pb80",
    "pb81",
    "pb82",
    "pb83",
    "pb84",
    "pb85"
})
public class ProjectInfoBeanJSExt {

    @XmlElementRef(name = "pb01", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb01;
    @XmlElementRef(name = "pb70", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb70;
    @XmlElementRef(name = "pb71", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb71;
    @XmlElementRef(name = "pb72", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb72;
    @XmlElementRef(name = "pb73", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb73;
    @XmlElementRef(name = "pb74", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb74;
    @XmlElementRef(name = "pb75", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb75;
    @XmlElementRef(name = "pb76", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb76;
    @XmlElementRef(name = "pb77", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb77;
    @XmlElementRef(name = "pb78", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb78;
    @XmlElementRef(name = "pb79", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb79;
    @XmlElementRef(name = "pb80", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb80;
    @XmlElementRef(name = "pb81", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb81;
    @XmlElementRef(name = "pb82", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb82;
    @XmlElementRef(name = "pb83", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb83;
    @XmlElementRef(name = "pb84", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb84;
    @XmlElementRef(name = "pb85", namespace = "http://bean.platform.webserviceinfo.neusoft.com", type = JAXBElement.class, required = false)
    protected JAXBElement<String> pb85;

    /**
     * 获取pb01属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb01() {
        return pb01;
    }

    /**
     * 设置pb01属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb01(JAXBElement<String> value) {
        this.pb01 = value;
    }

    /**
     * 获取pb70属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb70() {
        return pb70;
    }

    /**
     * 设置pb70属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb70(JAXBElement<String> value) {
        this.pb70 = value;
    }

    /**
     * 获取pb71属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb71() {
        return pb71;
    }

    /**
     * 设置pb71属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb71(JAXBElement<String> value) {
        this.pb71 = value;
    }

    /**
     * 获取pb72属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb72() {
        return pb72;
    }

    /**
     * 设置pb72属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb72(JAXBElement<String> value) {
        this.pb72 = value;
    }

    /**
     * 获取pb73属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb73() {
        return pb73;
    }

    /**
     * 设置pb73属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb73(JAXBElement<String> value) {
        this.pb73 = value;
    }

    /**
     * 获取pb74属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb74() {
        return pb74;
    }

    /**
     * 设置pb74属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb74(JAXBElement<String> value) {
        this.pb74 = value;
    }

    /**
     * 获取pb75属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb75() {
        return pb75;
    }

    /**
     * 设置pb75属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb75(JAXBElement<String> value) {
        this.pb75 = value;
    }

    /**
     * 获取pb76属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb76() {
        return pb76;
    }

    /**
     * 设置pb76属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb76(JAXBElement<String> value) {
        this.pb76 = value;
    }

    /**
     * 获取pb77属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb77() {
        return pb77;
    }

    /**
     * 设置pb77属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb77(JAXBElement<String> value) {
        this.pb77 = value;
    }

    /**
     * 获取pb78属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb78() {
        return pb78;
    }

    /**
     * 设置pb78属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb78(JAXBElement<String> value) {
        this.pb78 = value;
    }

    /**
     * 获取pb79属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb79() {
        return pb79;
    }

    /**
     * 设置pb79属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb79(JAXBElement<String> value) {
        this.pb79 = value;
    }

    /**
     * 获取pb80属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb80() {
        return pb80;
    }

    /**
     * 设置pb80属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb80(JAXBElement<String> value) {
        this.pb80 = value;
    }

    /**
     * 获取pb81属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb81() {
        return pb81;
    }

    /**
     * 设置pb81属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb81(JAXBElement<String> value) {
        this.pb81 = value;
    }

    /**
     * 获取pb82属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb82() {
        return pb82;
    }

    /**
     * 设置pb82属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb82(JAXBElement<String> value) {
        this.pb82 = value;
    }

    /**
     * 获取pb83属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb83() {
        return pb83;
    }

    /**
     * 设置pb83属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb83(JAXBElement<String> value) {
        this.pb83 = value;
    }

    /**
     * 获取pb84属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb84() {
        return pb84;
    }

    /**
     * 设置pb84属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb84(JAXBElement<String> value) {
        this.pb84 = value;
    }

    /**
     * 获取pb85属性的值。
     * 
     * @return
     *     possible object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public JAXBElement<String> getPb85() {
        return pb85;
    }

    /**
     * 设置pb85属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link JAXBElement }{@code <}{@link String }{@code >}
     *     
     */
    public void setPb85(JAXBElement<String> value) {
        this.pb85 = value;
    }

}
