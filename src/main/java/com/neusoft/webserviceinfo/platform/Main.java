package com.neusoft.webserviceinfo.platform;

import com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoIA;
import com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoIAPortType;


import java.io.FileWriter;
import java.util.ArrayList;
import java.util.List;

public class Main {

    public static void main(String[] args) {


        List<String> inputList = new ArrayList<>();
        String input1 = "2506-321062-89-01-579366";
        String input2 = "2505-320324-89-01-254346";
        String input3 = "2506-320921-89-01-138659";
        inputList.add(input1);
        inputList.add(input2);
        inputList.add(input3);


        try {

            ProjectBaseInfoIA projectBaseInfo = new ProjectBaseInfoIA();

            ProjectBaseInfoIAPortType portType = projectBaseInfo.getProjectBaseInfoIAHttpPort();

            int i = 1;
            for (String input : inputList) {
                System.out.println("Query Start：" + i);

                // http://10.114.1.23:20080/service/projectbaseinfo/projectQueryInfoByKeyCode?in0=4022025ii5d8jnm87ls89e878831001e&in1=input
                String result = portType.projectQueryInfoByKeyCode("4022025ii5d8jnm87ls89e878831001e", input);
                System.out.println("Query End：" + i);

                System.out.println("Write Start：" + i);
                displayResult(result, i);
                System.out.println("Write End：" + i);
                i++;
            }
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("error: " + e.getMessage());
        }
    }

    private static void displayResult(String result, int i) {

        if (result == null || result.isEmpty()) {
            System.out.println("No Result!!!");
            return;
        }

        try {
            FileWriter fileWriter = new FileWriter(i + "output.txt");
            fileWriter.write(result);
            fileWriter.close();
        } catch (java.io.IOException e) {
            e.printStackTrace();
        }

    }

}
