#!/bin/bash

# 项目基础信息Web服务测试运行脚本
# 用于在CentOS上运行测试

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_HOME="$(dirname "$SCRIPT_DIR")"

# 设置Java环境
JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-1.8.0-openjdk}
JAVA_CMD="$JAVA_HOME/bin/java"

# 检查Java是否可用
if [ ! -x "$JAVA_CMD" ]; then
    echo "错误: 找不到Java可执行文件: $JAVA_CMD"
    echo "请确保已安装Java 8并设置JAVA_HOME环境变量"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$("$JAVA_CMD" -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "使用Java版本: $JAVA_VERSION"

# 设置类路径
MAIN_CLASSES="$PROJECT_HOME/classes"
TEST_CLASSES="$PROJECT_HOME/test-classes"
DEPENDENCIES="$PROJECT_HOME/lib/dependencies/*"
TEST_DEPENDENCIES="$PROJECT_HOME/lib/test-dependencies/*"

CLASSPATH="$TEST_CLASSES:$MAIN_CLASSES:$DEPENDENCIES:$TEST_DEPENDENCIES"

# 检查必要的目录是否存在
if [ ! -d "$MAIN_CLASSES" ]; then
    echo "错误: 找不到主类目录: $MAIN_CLASSES"
    exit 1
fi

if [ ! -d "$TEST_CLASSES" ]; then
    echo "错误: 找不到测试类目录: $TEST_CLASSES"
    exit 1
fi

# 设置JVM参数
JVM_OPTS="-Xms256m -Xmx512m"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Djava.net.useSystemProxies=true"

# 设置日志配置
LOG_DIR="$PROJECT_HOME/logs"
mkdir -p "$LOG_DIR"

# 输出运行信息
echo "=========================================="
echo "项目基础信息Web服务测试工具"
echo "=========================================="
echo "项目目录: $PROJECT_HOME"
echo "主类目录: $MAIN_CLASSES"
echo "测试类目录: $TEST_CLASSES"
echo "日志目录: $LOG_DIR"
echo "JVM参数: $JVM_OPTS"
echo "=========================================="

# 解析命令行参数
TEST_TYPE="unit"
GENERATE_REPORT="false"
INTEGRATION_TEST="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        --unit)
            TEST_TYPE="unit"
            shift
            ;;
        --integration)
            TEST_TYPE="integration"
            INTEGRATION_TEST="true"
            shift
            ;;
        --all)
            TEST_TYPE="all"
            shift
            ;;
        --report)
            GENERATE_REPORT="true"
            shift
            ;;
        --help|-h)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --unit        运行单元测试 (默认)"
            echo "  --integration 运行集成测试"
            echo "  --all         运行所有测试"
            echo "  --report      生成测试报告"
            echo "  --help, -h    显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

cd "$PROJECT_HOME"

# 运行测试
case $TEST_TYPE in
    "unit")
        echo "正在运行单元测试..."
        "$JAVA_CMD" $JVM_OPTS -cp "$CLASSPATH" com.neusoft.webserviceinfo.platform.ProjectBaseInfoWebServiceTest
        ;;
    "integration")
        if [ "$INTEGRATION_TEST" = "true" ]; then
            echo "正在运行集成测试..."
            echo "注意: 集成测试需要Web服务可用"
            "$JAVA_CMD" $JVM_OPTS -cp "$CLASSPATH" com.neusoft.webserviceinfo.platform.ProjectBaseInfoIntegrationTest
        else
            echo "集成测试已禁用，请使用 --integration 参数启用"
        fi
        ;;
    "all")
        echo "正在运行所有测试..."
        "$JAVA_CMD" $JVM_OPTS -cp "$CLASSPATH" com.neusoft.webserviceinfo.platform.TestRunner
        ;;
esac

EXIT_CODE=$?

# 生成测试报告
if [ "$GENERATE_REPORT" = "true" ]; then
    echo ""
    echo "正在生成测试报告..."
    "$JAVA_CMD" $JVM_OPTS -cp "$CLASSPATH" com.neusoft.webserviceinfo.platform.TestReportGenerator
    
    # 查找生成的报告文件
    REPORT_FILE=$(ls test-report-*.md 2>/dev/null | head -1)
    if [ -n "$REPORT_FILE" ]; then
        echo "测试报告已生成: $REPORT_FILE"
    fi
fi

if [ $EXIT_CODE -eq 0 ]; then
    echo ""
    echo "测试执行完成"
else
    echo ""
    echo "测试执行失败，退出码: $EXIT_CODE"
fi

exit $EXIT_CODE
