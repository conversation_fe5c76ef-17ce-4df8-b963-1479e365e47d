#!/bin/bash

# 项目基础信息Web服务查询工具启动脚本
# 用于在CentOS上运行主程序

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_HOME="$(dirname "$SCRIPT_DIR")"

# 设置Java环境
JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-1.8.0-openjdk}
JAVA_CMD="$JAVA_HOME/bin/java"

# 检查Java是否可用
if [ ! -x "$JAVA_CMD" ]; then
    echo "错误: 找不到Java可执行文件: $JAVA_CMD"
    echo "请确保已安装Java 8并设置JAVA_HOME环境变量"
    exit 1
fi

# 检查Java版本
JAVA_VERSION=$("$JAVA_CMD" -version 2>&1 | awk -F '"' '/version/ {print $2}')
echo "使用Java版本: $JAVA_VERSION"

# 设置类路径
MAIN_JAR="$PROJECT_HOME/lib/query-1.0-SNAPSHOT.jar"
CLASSPATH="$MAIN_JAR"

# 检查主JAR文件是否存在
if [ ! -f "$MAIN_JAR" ]; then
    echo "错误: 找不到主JAR文件: $MAIN_JAR"
    echo "请确保项目已正确打包"
    exit 1
fi

# 设置JVM参数
JVM_OPTS="-Xms256m -Xmx512m"
JVM_OPTS="$JVM_OPTS -Dfile.encoding=UTF-8"
JVM_OPTS="$JVM_OPTS -Djava.net.useSystemProxies=true"

# 设置日志配置
LOG_DIR="$PROJECT_HOME/logs"
mkdir -p "$LOG_DIR"

# 输出运行信息
echo "=========================================="
echo "项目基础信息Web服务查询工具"
echo "=========================================="
echo "项目目录: $PROJECT_HOME"
echo "主JAR文件: $MAIN_JAR"
echo "日志目录: $LOG_DIR"
echo "JVM参数: $JVM_OPTS"
echo "=========================================="

# 运行主程序
echo "正在启动主程序..."
cd "$PROJECT_HOME"

"$JAVA_CMD" $JVM_OPTS -cp "$CLASSPATH" com.neusoft.webserviceinfo.platform.Main "$@"

EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo "程序执行完成"
else
    echo "程序执行失败，退出码: $EXIT_CODE"
fi

exit $EXIT_CODE
