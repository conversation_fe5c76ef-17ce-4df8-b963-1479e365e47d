#!/bin/bash

# CentOS环境设置脚本
# 用于在CentOS上安装和配置运行环境

echo "=========================================="
echo "CentOS环境设置脚本"
echo "=========================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

# 检查CentOS版本
if [ -f /etc/centos-release ]; then
    CENTOS_VERSION=$(cat /etc/centos-release)
    echo "检测到系统: $CENTOS_VERSION"
else
    echo "警告: 未检测到CentOS系统"
fi

# 更新系统
echo "正在更新系统包..."
yum update -y

# 安装Java 8
echo "正在安装Java 8..."
yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 验证Java安装
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
if [ -n "$JAVA_VERSION" ]; then
    echo "Java安装成功，版本: $JAVA_VERSION"
else
    echo "错误: Java安装失败"
    exit 1
fi

# 设置JAVA_HOME
JAVA_HOME_PATH="/usr/lib/jvm/java-1.8.0-openjdk"
echo "export JAVA_HOME=$JAVA_HOME_PATH" >> /etc/environment
echo "export PATH=\$PATH:\$JAVA_HOME/bin" >> /etc/environment

# 安装其他必要工具
echo "正在安装其他必要工具..."
yum install -y wget curl unzip tar

# 安装Maven (可选)
echo "是否安装Maven? (y/n)"
read -r INSTALL_MAVEN
if [ "$INSTALL_MAVEN" = "y" ] || [ "$INSTALL_MAVEN" = "Y" ]; then
    echo "正在安装Maven..."
    yum install -y maven
    
    MAVEN_VERSION=$(mvn -version 2>/dev/null | head -1)
    if [ -n "$MAVEN_VERSION" ]; then
        echo "Maven安装成功: $MAVEN_VERSION"
    else
        echo "警告: Maven安装可能失败"
    fi
fi

# 创建应用用户
APP_USER="webservice"
if ! id "$APP_USER" &>/dev/null; then
    echo "正在创建应用用户: $APP_USER"
    useradd -m -s /bin/bash "$APP_USER"
    echo "用户 $APP_USER 创建成功"
else
    echo "用户 $APP_USER 已存在"
fi

# 创建应用目录
APP_DIR="/opt/webservice-query"
echo "正在创建应用目录: $APP_DIR"
mkdir -p "$APP_DIR"
chown "$APP_USER:$APP_USER" "$APP_DIR"

# 设置防火墙规则 (如果需要)
echo "是否配置防火墙规则? (y/n)"
read -r CONFIGURE_FIREWALL
if [ "$CONFIGURE_FIREWALL" = "y" ] || [ "$CONFIGURE_FIREWALL" = "Y" ]; then
    echo "正在配置防火墙..."
    # 这里可以添加具体的防火墙规则
    systemctl enable firewalld
    systemctl start firewalld
    echo "防火墙配置完成"
fi

# 创建日志目录
LOG_DIR="/var/log/webservice-query"
echo "正在创建日志目录: $LOG_DIR"
mkdir -p "$LOG_DIR"
chown "$APP_USER:$APP_USER" "$LOG_DIR"

# 输出环境信息
echo ""
echo "=========================================="
echo "环境设置完成"
echo "=========================================="
echo "Java版本: $JAVA_VERSION"
echo "Java路径: $JAVA_HOME_PATH"
echo "应用用户: $APP_USER"
echo "应用目录: $APP_DIR"
echo "日志目录: $LOG_DIR"
echo ""
echo "下一步操作:"
echo "1. 将项目包上传到 $APP_DIR"
echo "2. 解压项目包"
echo "3. 使用 $APP_USER 用户运行应用"
echo ""
echo "示例命令:"
echo "  su - $APP_USER"
echo "  cd $APP_DIR"
echo "  tar -xzf query-1.0-SNAPSHOT-distribution.tar.gz"
echo "  cd query-1.0-SNAPSHOT"
echo "  ./bin/run-tests.sh --all --report"
echo "=========================================="
