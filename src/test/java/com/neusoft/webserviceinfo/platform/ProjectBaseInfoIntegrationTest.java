package com.neusoft.webserviceinfo.platform;

import com.neusoft.webserviceinfo.platform.bean.*;
import com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoIA;
import com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoIAPortType;
import org.junit.Before;
import org.junit.Test;
import org.junit.Ignore;

import javax.xml.ws.WebServiceException;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 项目基础信息Web服务集成测试类
 * 
 * 这个测试类用于测试真实的Web服务连接
 * 注意: 这些测试需要Web服务可用才能运行
 * 
 * 使用@Ignore注解暂时禁用，当需要测试真实服务时可以移除
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ProjectBaseInfoIntegrationTest {

    private ProjectBaseInfoIA service;
    private ProjectBaseInfoIAPortType portType;
    
    // 测试用的常量
    private static final String AUTH_CODE = "4022025ii5d8jnm87ls89e878831001e";
    private static final String[] TEST_PROJECT_CODES = {
        "2506-321062-89-01-579366",
        "2505-320324-89-01-254346", 
        "2506-320921-89-01-138659"
    };
    
    @Before
    public void setUp() {
        System.out.println("=== 初始化Web服务连接 ===");
        try {
            service = new ProjectBaseInfoIA();
            portType = service.getProjectBaseInfoIAHttpPort();
            System.out.println("Web服务连接初始化成功");
        } catch (Exception e) {
            System.err.println("Web服务连接初始化失败: " + e.getMessage());
            // 在集成测试中，如果服务不可用，我们可以跳过测试
        }
    }

    /**
     * 测试Web服务连接是否可用
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testWebServiceConnection() {
        System.out.println("=== 测试Web服务连接 ===");
        
        assertNotNull("服务实例不应为空", service);
        assertNotNull("端口类型不应为空", portType);
        
        System.out.println("Web服务连接测试通过");
    }

    /**
     * 测试真实的项目查询功能
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testRealProjectQuery() {
        System.out.println("=== 测试真实项目查询 ===");
        
        assumeServiceAvailable();
        
        for (int i = 0; i < TEST_PROJECT_CODES.length; i++) {
            String projectCode = TEST_PROJECT_CODES[i];
            System.out.println("查询项目 " + (i + 1) + ": " + projectCode);
            
            try {
                String result = portType.projectQueryInfoByKeyCode(AUTH_CODE, projectCode);
                
                System.out.println("查询结果长度: " + (result != null ? result.length() : 0));
                
                if (result != null && !result.isEmpty()) {
                    System.out.println("查询成功，结果预览: " + 
                        (result.length() > 200 ? result.substring(0, 200) + "..." : result));
                    
                    // 基本验证
                    assertTrue("结果应包含XML内容", result.contains("<") || result.contains("<?xml"));
                } else {
                    System.out.println("查询结果为空，可能项目不存在");
                }
                
                // 添加延迟避免频繁请求
                Thread.sleep(1000);
                
            } catch (WebServiceException e) {
                System.err.println("Web服务调用失败: " + e.getMessage());
                fail("Web服务调用失败: " + e.getMessage());
            } catch (Exception e) {
                System.err.println("查询过程中发生异常: " + e.getMessage());
                e.printStackTrace();
            }
        }
        
        System.out.println("真实项目查询测试完成");
    }

    /**
     * 测试项目基础信息查询
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testProjectBaseInfoQuery() {
        System.out.println("=== 测试项目基础信息查询 ===");
        
        assumeServiceAvailable();
        
        try {
            String testProjectCode = TEST_PROJECT_CODES[0];
            ArrayOfProjectBaseInfo result = portType.projectBaseInfo(AUTH_CODE, testProjectCode);
            
            if (result != null) {
                List<ProjectBaseInfo> projects = result.getProjectBaseInfo();
                System.out.println("获取到项目数量: " + (projects != null ? projects.size() : 0));
                
                if (projects != null && !projects.isEmpty()) {
                    ProjectBaseInfo firstProject = projects.get(0);
                    System.out.println("第一个项目信息:");
                    printProjectBaseInfo(firstProject);
                }
            } else {
                System.out.println("未获取到项目基础信息");
            }
            
        } catch (Exception e) {
            System.err.println("项目基础信息查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("项目基础信息查询测试完成");
    }

    /**
     * 测试项目代码检查功能
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testProjectCodeCheck() {
        System.out.println("=== 测试项目代码检查 ===");
        
        assumeServiceAvailable();
        
        try {
            String testProjectCode = TEST_PROJECT_CODES[0];
            ResultInfo result = portType.checkProjectByCode(AUTH_CODE, testProjectCode, "CHECK");
            
            if (result != null) {
                System.out.println("检查结果:");
                printResultInfo(result);
                
                // 验证结果结构
                assertNotNull("结果不应为空", result);
            } else {
                System.out.println("检查结果为空");
            }
            
        } catch (Exception e) {
            System.err.println("项目代码检查失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("项目代码检查测试完成");
    }

    /**
     * 测试时间范围查询
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testTimeRangeQuery() {
        System.out.println("=== 测试时间范围查询 ===");
        
        assumeServiceAvailable();
        
        try {
            String startTime = "2024-01-01";
            String endTime = "2024-12-31";
            
            String result = portType.projectQueryInfoByTime(AUTH_CODE, startTime, endTime);
            
            System.out.println("时间范围查询结果长度: " + (result != null ? result.length() : 0));
            
            if (result != null && !result.isEmpty()) {
                System.out.println("查询成功，结果预览: " + 
                    (result.length() > 300 ? result.substring(0, 300) + "..." : result));
            } else {
                System.out.println("指定时间范围内无项目数据");
            }
            
        } catch (Exception e) {
            System.err.println("时间范围查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("时间范围查询测试完成");
    }

    /**
     * 测试项目和事项信息查询
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testProjectAndItemInfoQuery() {
        System.out.println("=== 测试项目和事项信息查询 ===");
        
        assumeServiceAvailable();
        
        try {
            String testProjectCode = TEST_PROJECT_CODES[0];
            String result = portType.queryProjectAndItemInfoByProCode(AUTH_CODE, testProjectCode);
            
            System.out.println("项目和事项信息查询结果长度: " + (result != null ? result.length() : 0));
            
            if (result != null && !result.isEmpty()) {
                System.out.println("查询成功，结果预览: " + 
                    (result.length() > 300 ? result.substring(0, 300) + "..." : result));
            } else {
                System.out.println("未获取到项目和事项信息");
            }
            
        } catch (Exception e) {
            System.err.println("项目和事项信息查询失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("项目和事项信息查询测试完成");
    }

    /**
     * 性能测试 - 批量查询
     */
    @Test
    @Ignore("需要真实Web服务可用时才能运行")
    public void testBatchQueryPerformance() {
        System.out.println("=== 批量查询性能测试 ===");
        
        assumeServiceAvailable();
        
        long startTime = System.currentTimeMillis();
        int successCount = 0;
        int failCount = 0;
        
        for (String projectCode : TEST_PROJECT_CODES) {
            try {
                String result = portType.projectQueryInfoByKeyCode(AUTH_CODE, projectCode);
                if (result != null && !result.isEmpty()) {
                    successCount++;
                } else {
                    failCount++;
                }
                
                // 添加延迟避免过于频繁的请求
                Thread.sleep(500);
                
            } catch (Exception e) {
                failCount++;
                System.err.println("查询失败: " + projectCode + " - " + e.getMessage());
            }
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        System.out.println("批量查询完成:");
        System.out.println("总耗时: " + totalTime + "ms");
        System.out.println("成功查询: " + successCount + "个");
        System.out.println("失败查询: " + failCount + "个");
        System.out.println("平均每次查询耗时: " + (totalTime / TEST_PROJECT_CODES.length) + "ms");
        
        // 性能断言
        assertTrue("成功率应大于0", successCount > 0);
        assertTrue("平均响应时间应小于10秒", (totalTime / TEST_PROJECT_CODES.length) < 10000);
    }

    // ==================== 辅助方法 ====================
    
    /**
     * 检查服务是否可用
     */
    private void assumeServiceAvailable() {
        if (portType == null) {
            System.out.println("Web服务不可用，跳过测试");
            org.junit.Assume.assumeTrue("Web服务不可用", false);
        }
    }
    
    /**
     * 打印ResultInfo信息
     */
    private void printResultInfo(ResultInfo result) {
        if (result == null) {
            System.out.println("  ResultInfo: null");
            return;
        }
        
        System.out.println("  ResultInfo:");
        if (result.getPm01() != null) {
            System.out.println("    pm01: " + result.getPm01().getValue());
        }
        if (result.getPm02() != null) {
            System.out.println("    pm02: " + result.getPm02().getValue());
        }
        if (result.getPm03() != null) {
            System.out.println("    pm03: " + result.getPm03().getValue());
        }
    }
    
    /**
     * 打印ProjectBaseInfo信息
     */
    private void printProjectBaseInfo(ProjectBaseInfo project) {
        if (project == null) {
            System.out.println("  ProjectBaseInfo: null");
            return;
        }
        
        System.out.println("  ProjectBaseInfo:");
        if (project.getPb01() != null) {
            System.out.println("    pb01: " + project.getPb01().getValue());
        }
        if (project.getPb02() != null) {
            System.out.println("    pb02: " + project.getPb02().getValue());
        }
        if (project.getPb03() != null) {
            System.out.println("    pb03: " + project.getPb03().getValue());
        }
        // 可以继续打印更多字段...
    }
}
