package com.neusoft.webserviceinfo.platform;

import com.neusoft.webserviceinfo.platform.bean.*;
import com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoIA;
import com.neusoft.webserviceinfo.platform.interaction.ProjectBaseInfoIAPortType;
import org.junit.Before;
import org.junit.Test;
import org.junit.After;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.xml.bind.JAXBElement;
import javax.xml.namespace.QName;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 项目基础信息Web服务测试类
 * 
 * 测试ProjectBaseInfoIAPortType接口提供的各种Web服务方法
 * 包括查询、添加、检查等功能的测试
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ProjectBaseInfoWebServiceTest {

    @Mock
    private ProjectBaseInfoIAPortType mockPortType;
    
    @Mock
    private ProjectBaseInfoIA mockService;
    
    private ProjectBaseInfoWebServiceTest testInstance;
    
    // 测试用的常量
    private static final String TEST_AUTH_CODE = "4022025ii5d8jnm87ls89e878831001e";
    private static final String TEST_PROJECT_CODE = "2506-321062-89-01-579366";
    private static final String TEST_PROJECT_NAME = "测试项目";
    private static final String TEST_START_TIME = "2024-01-01";
    private static final String TEST_END_TIME = "2024-12-31";
    
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        testInstance = this;
        
        // 模拟服务初始化
        when(mockService.getProjectBaseInfoIAHttpPort()).thenReturn(mockPortType);
    }
    
    @After
    public void tearDown() {
        // 清理资源
        mockPortType = null;
        mockService = null;
    }

    /**
     * 测试根据关键代码查询项目信息 - 成功场景
     */
    @Test
    public void testProjectQueryInfoByKeyCode_Success() {
        System.out.println("=== 测试: projectQueryInfoByKeyCode - 成功场景 ===");
        
        // 准备测试数据
        String expectedResult = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<projectInfo><projectCode>" + TEST_PROJECT_CODE + "</projectCode>" +
                "<projectName>" + TEST_PROJECT_NAME + "</projectName></projectInfo>";
        
        // 模拟方法调用
        when(mockPortType.projectQueryInfoByKeyCode(TEST_AUTH_CODE, TEST_PROJECT_CODE))
                .thenReturn(expectedResult);
        
        // 执行测试
        String result = mockPortType.projectQueryInfoByKeyCode(TEST_AUTH_CODE, TEST_PROJECT_CODE);
        
        // 验证结果
        assertNotNull("查询结果不应为空", result);
        assertTrue("结果应包含项目代码", result.contains(TEST_PROJECT_CODE));
        assertTrue("结果应包含项目名称", result.contains(TEST_PROJECT_NAME));
        
        // 验证方法调用
        verify(mockPortType, times(1)).projectQueryInfoByKeyCode(TEST_AUTH_CODE, TEST_PROJECT_CODE);
        
        System.out.println("查询结果: " + result);
        System.out.println("测试通过: projectQueryInfoByKeyCode 成功场景");
    }

    /**
     * 测试根据关键代码查询项目信息 - 空结果场景
     */
    @Test
    public void testProjectQueryInfoByKeyCode_EmptyResult() {
        System.out.println("=== 测试: projectQueryInfoByKeyCode - 空结果场景 ===");
        
        String invalidProjectCode = "INVALID-CODE";
        
        // 模拟返回空结果
        when(mockPortType.projectQueryInfoByKeyCode(TEST_AUTH_CODE, invalidProjectCode))
                .thenReturn("");
        
        // 执行测试
        String result = mockPortType.projectQueryInfoByKeyCode(TEST_AUTH_CODE, invalidProjectCode);
        
        // 验证结果
        assertNotNull("结果不应为null", result);
        assertEquals("结果应为空字符串", "", result);
        
        verify(mockPortType, times(1)).projectQueryInfoByKeyCode(TEST_AUTH_CODE, invalidProjectCode);
        
        System.out.println("测试通过: projectQueryInfoByKeyCode 空结果场景");
    }

    /**
     * 测试根据时间查询项目信息
     */
    @Test
    public void testProjectQueryInfoByTime_Success() {
        System.out.println("=== 测试: projectQueryInfoByTime - 成功场景 ===");
        
        // 准备测试数据
        String expectedResult = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<projectList><project><startTime>" + TEST_START_TIME + "</startTime>" +
                "<endTime>" + TEST_END_TIME + "</endTime></project></projectList>";
        
        // 模拟方法调用
        when(mockPortType.projectQueryInfoByTime(TEST_AUTH_CODE, TEST_START_TIME, TEST_END_TIME))
                .thenReturn(expectedResult);
        
        // 执行测试
        String result = mockPortType.projectQueryInfoByTime(TEST_AUTH_CODE, TEST_START_TIME, TEST_END_TIME);
        
        // 验证结果
        assertNotNull("查询结果不应为空", result);
        assertTrue("结果应包含开始时间", result.contains(TEST_START_TIME));
        assertTrue("结果应包含结束时间", result.contains(TEST_END_TIME));
        
        verify(mockPortType, times(1)).projectQueryInfoByTime(TEST_AUTH_CODE, TEST_START_TIME, TEST_END_TIME);
        
        System.out.println("查询结果: " + result);
        System.out.println("测试通过: projectQueryInfoByTime 成功场景");
    }

    /**
     * 测试检查项目代码
     */
    @Test
    public void testCheckProjectByCode_Success() {
        System.out.println("=== 测试: checkProjectByCode - 成功场景 ===");
        
        // 创建模拟的ResultInfo对象
        ResultInfo mockResult = createMockResultInfo("SUCCESS", "项目代码有效", "检查通过");
        
        // 模拟方法调用
        when(mockPortType.checkProjectByCode(TEST_AUTH_CODE, TEST_PROJECT_CODE, "CHECK"))
                .thenReturn(mockResult);
        
        // 执行测试
        ResultInfo result = mockPortType.checkProjectByCode(TEST_AUTH_CODE, TEST_PROJECT_CODE, "CHECK");
        
        // 验证结果
        assertNotNull("检查结果不应为空", result);
        assertNotNull("pm01不应为空", result.getPm01());
        assertEquals("pm01应为SUCCESS", "SUCCESS", result.getPm01().getValue());
        
        verify(mockPortType, times(1)).checkProjectByCode(TEST_AUTH_CODE, TEST_PROJECT_CODE, "CHECK");
        
        System.out.println("检查结果: " + getResultInfoString(result));
        System.out.println("测试通过: checkProjectByCode 成功场景");
    }

    /**
     * 测试获取项目基础信息
     */
    @Test
    public void testProjectBaseInfo_Success() {
        System.out.println("=== 测试: projectBaseInfo - 成功场景 ===");
        
        // 创建模拟的ArrayOfProjectBaseInfo对象
        ArrayOfProjectBaseInfo mockArray = createMockArrayOfProjectBaseInfo();
        
        // 模拟方法调用
        when(mockPortType.projectBaseInfo(TEST_AUTH_CODE, TEST_PROJECT_CODE))
                .thenReturn(mockArray);
        
        // 执行测试
        ArrayOfProjectBaseInfo result = mockPortType.projectBaseInfo(TEST_AUTH_CODE, TEST_PROJECT_CODE);
        
        // 验证结果
        assertNotNull("结果不应为空", result);
        assertNotNull("项目列表不应为空", result.getProjectBaseInfo());
        assertFalse("项目列表不应为空列表", result.getProjectBaseInfo().isEmpty());
        
        verify(mockPortType, times(1)).projectBaseInfo(TEST_AUTH_CODE, TEST_PROJECT_CODE);
        
        System.out.println("获取到项目数量: " + result.getProjectBaseInfo().size());
        System.out.println("测试通过: projectBaseInfo 成功场景");
    }

    /**
     * 测试添加项目信息
     */
    @Test
    public void testAddProjectInfo_Success() {
        System.out.println("=== 测试: addProjectInfo - 成功场景 ===");
        
        // 创建测试用的ProjectInfoBean
        ProjectInfoBean projectInfo = createMockProjectInfoBean();
        
        // 创建模拟的ResultInfo对象
        ResultInfo mockResult = createMockResultInfo("SUCCESS", "项目添加成功", "操作完成");
        
        // 模拟方法调用
        when(mockPortType.addProjectInfo(TEST_AUTH_CODE, projectInfo))
                .thenReturn(mockResult);
        
        // 执行测试
        ResultInfo result = mockPortType.addProjectInfo(TEST_AUTH_CODE, projectInfo);
        
        // 验证结果
        assertNotNull("添加结果不应为空", result);
        assertNotNull("pm01不应为空", result.getPm01());
        assertEquals("pm01应为SUCCESS", "SUCCESS", result.getPm01().getValue());
        
        verify(mockPortType, times(1)).addProjectInfo(TEST_AUTH_CODE, projectInfo);
        
        System.out.println("添加结果: " + getResultInfoString(result));
        System.out.println("测试通过: addProjectInfo 成功场景");
    }

    /**
     * 测试查询项目和事项信息
     */
    @Test
    public void testQueryProjectAndItemInfoByProCode_Success() {
        System.out.println("=== 测试: queryProjectAndItemInfoByProCode - 成功场景 ===");
        
        // 准备测试数据
        String expectedResult = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<projectAndItemInfo><projectCode>" + TEST_PROJECT_CODE + "</projectCode>" +
                "<items><item>事项1</item><item>事项2</item></items></projectAndItemInfo>";
        
        // 模拟方法调用
        when(mockPortType.queryProjectAndItemInfoByProCode(TEST_AUTH_CODE, TEST_PROJECT_CODE))
                .thenReturn(expectedResult);
        
        // 执行测试
        String result = mockPortType.queryProjectAndItemInfoByProCode(TEST_AUTH_CODE, TEST_PROJECT_CODE);
        
        // 验证结果
        assertNotNull("查询结果不应为空", result);
        assertTrue("结果应包含项目代码", result.contains(TEST_PROJECT_CODE));
        assertTrue("结果应包含事项信息", result.contains("事项"));
        
        verify(mockPortType, times(1)).queryProjectAndItemInfoByProCode(TEST_AUTH_CODE, TEST_PROJECT_CODE);
        
        System.out.println("查询结果: " + result);
        System.out.println("测试通过: queryProjectAndItemInfoByProCode 成功场景");
    }

    /**
     * 测试异常处理 - 无效认证码
     */
    @Test(expected = RuntimeException.class)
    public void testInvalidAuthCode_ThrowsException() {
        System.out.println("=== 测试: 无效认证码异常处理 ===");
        
        String invalidAuthCode = "INVALID_AUTH_CODE";
        
        // 模拟抛出异常
        when(mockPortType.projectQueryInfoByKeyCode(invalidAuthCode, TEST_PROJECT_CODE))
                .thenThrow(new RuntimeException("认证失败"));
        
        // 执行测试 - 应该抛出异常
        mockPortType.projectQueryInfoByKeyCode(invalidAuthCode, TEST_PROJECT_CODE);
        
        System.out.println("测试通过: 无效认证码异常处理");
    }

    // ==================== 辅助方法 ====================
    
    /**
     * 创建模拟的ResultInfo对象
     */
    private ResultInfo createMockResultInfo(String pm01Value, String pm02Value, String pm03Value) {
        ResultInfo result = new ResultInfo();
        
        // 创建JAXBElement
        QName qname1 = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pm01");
        QName qname2 = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pm02");
        QName qname3 = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pm03");
        
        JAXBElement<String> pm01 = new JAXBElement<>(qname1, String.class, pm01Value);
        JAXBElement<String> pm02 = new JAXBElement<>(qname2, String.class, pm02Value);
        JAXBElement<String> pm03 = new JAXBElement<>(qname3, String.class, pm03Value);
        
        result.setPm01(pm01);
        result.setPm02(pm02);
        result.setPm03(pm03);
        
        return result;
    }
    
    /**
     * 创建模拟的ArrayOfProjectBaseInfo对象
     */
    private ArrayOfProjectBaseInfo createMockArrayOfProjectBaseInfo() {
        ArrayOfProjectBaseInfo array = new ArrayOfProjectBaseInfo();
        List<ProjectBaseInfo> list = array.getProjectBaseInfo();
        
        // 添加一个测试项目
        ProjectBaseInfo project = new ProjectBaseInfo();
        QName qname = new QName("http://bean.platform.webserviceinfo.neusoft.com", "pb01");
        JAXBElement<String> pb01 = new JAXBElement<>(qname, String.class, TEST_PROJECT_CODE);
        project.setPb01(pb01);
        
        list.add(project);
        
        return array;
    }
    
    /**
     * 创建模拟的ProjectInfoBean对象
     */
    private ProjectInfoBean createMockProjectInfoBean() {
        ProjectInfoBean bean = new ProjectInfoBean();
        
        // 设置项目代码
        QName qname = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_CODE");
        JAXBElement<String> projectCode = new JAXBElement<>(qname, String.class, TEST_PROJECT_CODE);
        bean.setPROJECTCODE(projectCode);
        
        // 设置项目名称
        QName qname2 = new QName("http://bean.platform.webserviceinfo.neusoft.com", "PROJECT_NAME");
        JAXBElement<String> projectName = new JAXBElement<>(qname2, String.class, TEST_PROJECT_NAME);
        bean.setPROJECTNAME(projectName);
        
        return bean;
    }
    
    /**
     * 获取ResultInfo的字符串表示
     */
    private String getResultInfoString(ResultInfo result) {
        if (result == null) return "null";
        
        StringBuilder sb = new StringBuilder();
        sb.append("ResultInfo{");
        
        if (result.getPm01() != null) {
            sb.append("pm01=").append(result.getPm01().getValue()).append(", ");
        }
        if (result.getPm02() != null) {
            sb.append("pm02=").append(result.getPm02().getValue()).append(", ");
        }
        if (result.getPm03() != null) {
            sb.append("pm03=").append(result.getPm03().getValue());
        }
        
        sb.append("}");
        return sb.toString();
    }
}
