package com.neusoft.webserviceinfo.platform;

import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * 测试套件 - 运行所有测试类
 * 
 * 包含单元测试和集成测试
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    ProjectBaseInfoWebServiceTest.class,
    ProjectBaseInfoIntegrationTest.class
})
public class AllTestsSuite {
    // 测试套件类，无需实现内容
    // JUnit会自动运行@Suite.SuiteClasses中指定的所有测试类
}
