package com.neusoft.webserviceinfo.platform;

import org.junit.runner.JUnitCore;
import org.junit.runner.Result;
import org.junit.runner.notification.Failure;

/**
 * 测试运行器
 * 
 * 用于手动运行测试并输出详细结果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("==========================================");
        System.out.println("项目基础信息Web服务测试开始");
        System.out.println("==========================================");
        
        // 运行单元测试
        System.out.println("\n>>> 运行单元测试 <<<");
        runTest(ProjectBaseInfoWebServiceTest.class);
        
        // 运行集成测试（如果需要的话）
        System.out.println("\n>>> 集成测试（已禁用，需要真实服务） <<<");
        System.out.println("集成测试已使用@Ignore注解禁用");
        System.out.println("如需运行集成测试，请:");
        System.out.println("1. 确保Web服务 http://10.114.1.23:20080/service/projectbaseinfo 可访问");
        System.out.println("2. 移除ProjectBaseInfoIntegrationTest类中的@Ignore注解");
        System.out.println("3. 重新运行测试");
        
        // 运行完整测试套件
        System.out.println("\n>>> 运行测试套件 <<<");
        runTest(AllTestsSuite.class);
        
        System.out.println("\n==========================================");
        System.out.println("所有测试执行完成");
        System.out.println("==========================================");
    }
    
    /**
     * 运行指定的测试类
     */
    private static void runTest(Class<?> testClass) {
        System.out.println("运行测试类: " + testClass.getSimpleName());
        System.out.println("------------------------------------------");
        
        JUnitCore junit = new JUnitCore();
        Result result = junit.run(testClass);
        
        // 输出测试结果
        System.out.println("测试结果统计:");
        System.out.println("  运行测试数: " + result.getRunCount());
        System.out.println("  失败测试数: " + result.getFailureCount());
        System.out.println("  忽略测试数: " + result.getIgnoreCount());
        System.out.println("  运行时间: " + result.getRunTime() + "ms");
        System.out.println("  测试成功: " + (result.wasSuccessful() ? "是" : "否"));
        
        // 输出失败详情
        if (result.getFailureCount() > 0) {
            System.out.println("\n失败测试详情:");
            for (Failure failure : result.getFailures()) {
                System.out.println("  - " + failure.getTestHeader());
                System.out.println("    错误: " + failure.getMessage());
                if (failure.getException() != null) {
                    System.out.println("    异常: " + failure.getException().getClass().getSimpleName());
                }
            }
        }
        
        System.out.println("------------------------------------------\n");
    }
}
