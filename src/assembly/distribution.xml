<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 
          http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    
    <id>distribution</id>
    <formats>
        <format>tar.gz</format>
        <format>zip</format>
    </formats>
    
    <includeBaseDirectory>true</includeBaseDirectory>
    <baseDirectory>${project.artifactId}-${project.version}</baseDirectory>
    
    <fileSets>
        <!-- Include the shaded JAR -->
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>lib</outputDirectory>
            <includes>
                <include>${project.artifactId}-${project.version}.jar</include>
            </includes>
        </fileSet>
        
        <!-- Include scripts -->
        <fileSet>
            <directory>src/main/scripts</directory>
            <outputDirectory>bin</outputDirectory>
            <fileMode>0755</fileMode>
            <includes>
                <include>**/*</include>
            </includes>
        </fileSet>
        
        <!-- Include configuration files -->
        <fileSet>
            <directory>src/main/config</directory>
            <outputDirectory>config</outputDirectory>
            <includes>
                <include>**/*</include>
            </includes>
        </fileSet>
        
        <!-- Include documentation -->
        <fileSet>
            <directory>.</directory>
            <outputDirectory>docs</outputDirectory>
            <includes>
                <include>*.md</include>
                <include>test-report-*.md</include>
            </includes>
        </fileSet>
        
        <!-- Include test classes for running tests -->
        <fileSet>
            <directory>${project.build.directory}/test-classes</directory>
            <outputDirectory>test-classes</outputDirectory>
            <includes>
                <include>**/*</include>
            </includes>
        </fileSet>
        
        <!-- Include main classes -->
        <fileSet>
            <directory>${project.build.directory}/classes</directory>
            <outputDirectory>classes</outputDirectory>
            <includes>
                <include>**/*</include>
            </includes>
        </fileSet>
    </fileSets>
    
    <!-- Include dependencies -->
    <dependencySets>
        <dependencySet>
            <outputDirectory>lib/dependencies</outputDirectory>
            <useProjectArtifact>false</useProjectArtifact>
            <unpack>false</unpack>
            <scope>runtime</scope>
        </dependencySet>
        
        <dependencySet>
            <outputDirectory>lib/test-dependencies</outputDirectory>
            <useProjectArtifact>false</useProjectArtifact>
            <unpack>false</unpack>
            <scope>test</scope>
        </dependencySet>
    </dependencySets>
</assembly>
