# CentOS部署指南

## 概述

本指南详细说明如何将项目基础信息Web服务查询工具打包并在CentOS系统上部署运行。

## 前置要求

### 开发环境要求
- Java 8 或更高版本
- Maven 3.6 或更高版本
- 网络连接（用于下载依赖）

### 目标CentOS环境要求
- CentOS 7 或更高版本
- Java 8 运行时环境
- 至少512MB可用内存
- 至少100MB磁盘空间

## 打包步骤

### 1. 编译和打包项目

```bash
# 在项目根目录执行
mvn clean compile test-compile package

# 或者跳过测试打包（如果测试环境不可用）
mvn clean compile test-compile package -DskipTests
```

### 2. 验证打包结果

打包完成后，检查以下文件是否生成：

```bash
target/
├── query-1.0-SNAPSHOT.jar                    # 主程序JAR
├── query-1.0-SNAPSHOT-distribution.tar.gz    # 完整分发包（推荐）
├── query-1.0-SNAPSHOT-distribution.zip       # 完整分发包（Windows）
└── classes/                                   # 编译后的类文件
```

### 3. 选择部署包

**推荐使用**: `query-1.0-SNAPSHOT-distribution.tar.gz`

这个包包含：
- 主程序JAR文件
- 所有依赖库
- 运行脚本
- 配置文件
- 文档

## CentOS部署步骤

### 1. 准备CentOS环境

#### 方法一：使用自动化脚本（推荐）

```bash
# 上传setup-centos.sh到CentOS服务器
scp src/main/scripts/setup-centos.sh user@centos-server:/tmp/

# 在CentOS服务器上执行
sudo chmod +x /tmp/setup-centos.sh
sudo /tmp/setup-centos.sh
```

#### 方法二：手动安装

```bash
# 更新系统
sudo yum update -y

# 安装Java 8
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 验证Java安装
java -version

# 设置JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
source ~/.bashrc

# 创建应用目录
sudo mkdir -p /opt/webservice-query
sudo chown $USER:$USER /opt/webservice-query
```

### 2. 上传和解压项目包

```bash
# 上传分发包到CentOS服务器
scp target/query-1.0-SNAPSHOT-distribution.tar.gz user@centos-server:/opt/webservice-query/

# 在CentOS服务器上解压
cd /opt/webservice-query
tar -xzf query-1.0-SNAPSHOT-distribution.tar.gz
cd query-1.0-SNAPSHOT

# 设置脚本执行权限
chmod +x bin/*.sh
```

### 3. 验证部署

```bash
# 检查目录结构
ls -la

# 应该看到以下结构：
# bin/           # 运行脚本
# lib/           # JAR文件和依赖
# config/        # 配置文件
# docs/          # 文档
# classes/       # 编译后的类文件
# test-classes/  # 测试类文件
```

## 运行测试

### 1. 运行单元测试

```bash
# 运行所有单元测试
./bin/run-tests.sh --unit

# 运行单元测试并生成报告
./bin/run-tests.sh --unit --report
```

### 2. 运行集成测试（可选）

```bash
# 注意：需要Web服务可用
./bin/run-tests.sh --integration

# 运行所有测试
./bin/run-tests.sh --all --report
```

### 3. 运行主程序

```bash
# 运行主程序进行实际查询
./bin/run-main.sh
```

## 配置说明

### 主要配置文件

- `config/application.properties` - 主配置文件

### 重要配置项

```properties
# Web服务地址
webservice.url=http://10.114.1.23:20080/service/projectbaseinfo

# 认证码
webservice.auth.code=4022025ii5d8jnm87ls89e878831001e

# 测试项目代码
test.project.codes=2506-321062-89-01-579366,2505-320324-89-01-254346,2506-320921-89-01-138659

# 输出目录
output.directory=./output
```

## 故障排除

### 常见问题

#### 1. Java版本问题

```bash
# 检查Java版本
java -version

# 如果版本不正确，设置JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk
export PATH=$JAVA_HOME/bin:$PATH
```

#### 2. 权限问题

```bash
# 确保脚本有执行权限
chmod +x bin/*.sh

# 确保目录有写权限
chmod 755 logs output
```

#### 3. 网络连接问题

```bash
# 测试Web服务连接
curl -I http://10.114.1.23:20080/service/projectbaseinfo

# 检查防火墙设置
sudo firewall-cmd --list-all
```

#### 4. 内存不足

```bash
# 修改JVM参数（在运行脚本中）
JVM_OPTS="-Xms128m -Xmx256m"
```

### 日志查看

```bash
# 查看应用日志
tail -f logs/application.log

# 查看系统日志
sudo journalctl -f
```

## 性能优化

### 1. JVM调优

在运行脚本中调整JVM参数：

```bash
# 对于内存较小的服务器
JVM_OPTS="-Xms128m -Xmx256m -XX:+UseG1GC"

# 对于内存充足的服务器
JVM_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"
```

### 2. 网络优化

```bash
# 调整网络超时设置
webservice.timeout=60000
network.connect.timeout=15000
network.read.timeout=60000
```

## 监控和维护

### 1. 设置定时任务

```bash
# 编辑crontab
crontab -e

# 添加定时测试任务（每天凌晨2点）
0 2 * * * cd /opt/webservice-query/query-1.0-SNAPSHOT && ./bin/run-tests.sh --unit --report
```

### 2. 日志轮转

```bash
# 创建logrotate配置
sudo vim /etc/logrotate.d/webservice-query

# 添加以下内容：
/opt/webservice-query/query-1.0-SNAPSHOT/logs/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 webservice webservice
}
```

## 安全建议

1. **用户权限**：使用专用用户运行应用，避免使用root
2. **网络安全**：配置防火墙规则，只开放必要端口
3. **文件权限**：设置适当的文件和目录权限
4. **日志安全**：定期清理和归档日志文件

## 备份和恢复

### 备份

```bash
# 备份整个应用目录
tar -czf webservice-query-backup-$(date +%Y%m%d).tar.gz /opt/webservice-query/

# 备份配置文件
cp config/application.properties config/application.properties.bak
```

### 恢复

```bash
# 恢复应用
tar -xzf webservice-query-backup-YYYYMMDD.tar.gz -C /

# 恢复配置
cp config/application.properties.bak config/application.properties
```

## 联系支持

如遇到部署问题，请检查：
1. 系统日志：`/var/log/messages`
2. 应用日志：`logs/application.log`
3. Java版本和环境变量
4. 网络连接和防火墙设置

提供以上信息有助于快速定位和解决问题。
