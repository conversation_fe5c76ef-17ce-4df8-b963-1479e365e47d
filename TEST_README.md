# 项目基础信息Web服务测试说明

## 概述

本项目包含了对项目基础信息Web服务的完整测试套件，包括单元测试和集成测试。

## 测试结构

```
src/test/java/com/neusoft/webserviceinfo/platform/
├── ProjectBaseInfoWebServiceTest.java      # 单元测试（使用Mock）
├── ProjectBaseInfoIntegrationTest.java     # 集成测试（真实服务）
├── AllTestsSuite.java                      # 测试套件
└── TestRunner.java                         # 测试运行器
```

## 测试类说明

### 1. ProjectBaseInfoWebServiceTest.java
- **类型**: 单元测试
- **特点**: 使用Mockito进行模拟测试，不依赖真实Web服务
- **测试内容**:
  - `projectQueryInfoByKeyCode()` - 根据关键代码查询项目信息
  - `projectQueryInfoByTime()` - 根据时间范围查询项目信息
  - `checkProjectByCode()` - 检查项目代码有效性
  - `projectBaseInfo()` - 获取项目基础信息
  - `addProjectInfo()` - 添加项目信息
  - `queryProjectAndItemInfoByProCode()` - 查询项目和事项信息
  - 异常处理测试

### 2. ProjectBaseInfoIntegrationTest.java
- **类型**: 集成测试
- **特点**: 测试真实的Web服务连接
- **状态**: 默认使用`@Ignore`注解禁用
- **测试内容**:
  - Web服务连接测试
  - 真实项目查询测试
  - 性能测试
  - 批量查询测试

## 运行测试

### 方法1: 使用Maven命令

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ProjectBaseInfoWebServiceTest

# 运行测试并生成报告
mvn test -Dmaven.test.failure.ignore=true
```

### 方法2: 使用IDE
1. 在IDE中右键点击测试类
2. 选择"Run Tests"或"Run as JUnit Test"

### 方法3: 使用测试运行器
```bash
# 编译项目
mvn compile test-compile

# 运行测试运行器
java -cp target/test-classes:target/classes:$(mvn dependency:build-classpath -Dmdep.outputFile=/dev/stdout -q) com.neusoft.webserviceinfo.platform.TestRunner
```

### 方法4: 运行测试套件
```java
// 在IDE中运行AllTestsSuite类
// 或使用JUnit运行器
```

## 集成测试说明

### 启用集成测试
集成测试默认被禁用（使用`@Ignore`注解），要启用集成测试：

1. **确保Web服务可访问**:
   - 服务地址: `http://***********:20080/service/projectbaseinfo`
   - 确保网络连接正常
   - 确保服务正在运行

2. **移除@Ignore注解**:
   ```java
   // 在ProjectBaseInfoIntegrationTest.java中
   // 注释或删除以下注解
   // @Ignore("需要真实Web服务可用时才能运行")
   ```

3. **运行集成测试**:
   ```bash
   mvn test -Dtest=ProjectBaseInfoIntegrationTest
   ```

### 集成测试注意事项
- 集成测试会发起真实的网络请求
- 测试数据使用项目中预定义的项目代码
- 包含性能测试，可能需要较长时间
- 建议在测试环境中运行

## 测试数据

### 测试用项目代码
```java
private static final String[] TEST_PROJECT_CODES = {
    "2506-321062-89-01-579366",
    "2505-320324-89-01-254346", 
    "2506-320921-89-01-138659"
};
```

### 认证码
```java
private static final String AUTH_CODE = "4022025ii5d8jnm87ls89e878831001e";
```

## 测试覆盖的Web服务方法

| 方法名 | 功能描述 | 测试状态 |
|--------|----------|----------|
| `projectQueryInfoByKeyCode` | 根据关键代码查询项目信息 | ✅ 已测试 |
| `projectQueryInfoByTime` | 根据时间范围查询项目信息 | ✅ 已测试 |
| `checkProjectByCode` | 检查项目代码有效性 | ✅ 已测试 |
| `projectBaseInfo` | 获取项目基础信息 | ✅ 已测试 |
| `addProjectInfo` | 添加项目信息 | ✅ 已测试 |
| `queryProjectAndItemInfoByProCode` | 查询项目和事项信息 | ✅ 已测试 |
| `projectBindingInfo` | 项目绑定信息 | ⏳ 待添加 |
| `addProjectInfo1` | 添加项目信息(扩展版) | ⏳ 待添加 |
| `projectQueryInfoByCode` | 根据代码查询项目信息 | ⏳ 待添加 |

## 测试结果示例

```
==========================================
项目基础信息Web服务测试开始
==========================================

>>> 运行单元测试 <<<
运行测试类: ProjectBaseInfoWebServiceTest
------------------------------------------
=== 测试: projectQueryInfoByKeyCode - 成功场景 ===
查询结果: <?xml version="1.0" encoding="UTF-8"?><projectInfo>...
测试通过: projectQueryInfoByKeyCode 成功场景

测试结果统计:
  运行测试数: 7
  失败测试数: 0
  忽略测试数: 0
  运行时间: 156ms
  测试成功: 是
------------------------------------------
```

## 故障排除

### 常见问题

1. **编译错误**:
   ```bash
   # 确保Maven依赖已下载
   mvn clean compile test-compile
   ```

2. **Web服务连接失败**:
   - 检查网络连接
   - 确认服务地址是否正确
   - 检查防火墙设置

3. **认证失败**:
   - 确认认证码是否有效
   - 检查认证码格式

4. **测试超时**:
   - 增加测试超时时间
   - 检查网络延迟

### 调试建议

1. **启用详细日志**:
   ```java
   System.setProperty("com.sun.xml.ws.transport.http.client.HttpTransportPipe.dump", "true");
   ```

2. **使用网络抓包工具**:
   - Wireshark
   - Fiddler
   - Charles

3. **检查WSDL**:
   ```bash
   curl http://***********:20080/service/projectbaseinfo?wsdl
   ```

## 扩展测试

### 添加新的测试方法
1. 在相应的测试类中添加新的`@Test`方法
2. 使用适当的断言验证结果
3. 添加必要的Mock设置（单元测试）

### 添加性能测试
1. 使用`System.currentTimeMillis()`测量执行时间
2. 设置合理的性能基准
3. 考虑并发测试场景

### 添加数据驱动测试
1. 使用`@Parameterized`注解
2. 准备测试数据集
3. 验证不同输入的结果

## 联系信息

如有测试相关问题，请联系开发团队。
