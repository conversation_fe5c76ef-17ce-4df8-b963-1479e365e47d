#!/bin/bash

# 验证打包结果脚本
# 用于检查生成的分发包是否正确

echo "=========================================="
echo "验证项目打包结果"
echo "=========================================="

# 检查必要文件是否存在
echo "1. 检查生成的文件..."

if [ -f "target/query-1.0-SNAPSHOT.jar" ]; then
    echo "✅ 主JAR文件存在: target/query-1.0-SNAPSHOT.jar"
    JAR_SIZE=$(ls -lh target/query-1.0-SNAPSHOT.jar | awk '{print $5}')
    echo "   文件大小: $JAR_SIZE"
else
    echo "❌ 主JAR文件不存在"
    exit 1
fi

if [ -f "target/query-1.0-SNAPSHOT-distribution.tar.gz" ]; then
    echo "✅ 分发包存在: target/query-1.0-SNAPSHOT-distribution.tar.gz"
    TAR_SIZE=$(ls -lh target/query-1.0-SNAPSHOT-distribution.tar.gz | awk '{print $5}')
    echo "   文件大小: $TAR_SIZE"
else
    echo "❌ 分发包不存在"
    exit 1
fi

if [ -f "target/query-1.0-SNAPSHOT-distribution.zip" ]; then
    echo "✅ ZIP分发包存在: target/query-1.0-SNAPSHOT-distribution.zip"
    ZIP_SIZE=$(ls -lh target/query-1.0-SNAPSHOT-distribution.zip | awk '{print $5}')
    echo "   文件大小: $ZIP_SIZE"
else
    echo "❌ ZIP分发包不存在"
    exit 1
fi

# 创建临时目录进行解压测试
TEMP_DIR="temp_verify"
echo ""
echo "2. 验证分发包内容..."

if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

mkdir -p "$TEMP_DIR"
cd "$TEMP_DIR"

# 解压tar.gz包
echo "正在解压 tar.gz 包..."
tar -xzf "../target/query-1.0-SNAPSHOT-distribution.tar.gz"

if [ $? -eq 0 ]; then
    echo "✅ tar.gz 包解压成功"
else
    echo "❌ tar.gz 包解压失败"
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 检查解压后的目录结构
EXTRACT_DIR="query-1.0-SNAPSHOT"
if [ -d "$EXTRACT_DIR" ]; then
    echo "✅ 解压目录存在: $EXTRACT_DIR"
    cd "$EXTRACT_DIR"
    
    echo ""
    echo "3. 检查目录结构..."
    
    # 检查必要的目录和文件
    REQUIRED_DIRS=("bin" "lib" "config" "docs" "classes" "test-classes")
    REQUIRED_FILES=("bin/run-main.sh" "bin/run-tests.sh" "bin/setup-centos.sh" "config/application.properties")
    
    for dir in "${REQUIRED_DIRS[@]}"; do
        if [ -d "$dir" ]; then
            echo "✅ 目录存在: $dir"
        else
            echo "❌ 目录缺失: $dir"
        fi
    done
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "✅ 文件存在: $file"
        else
            echo "❌ 文件缺失: $file"
        fi
    done
    
    echo ""
    echo "4. 检查脚本权限..."
    
    # 检查脚本是否有执行权限
    SCRIPTS=("bin/run-main.sh" "bin/run-tests.sh" "bin/setup-centos.sh")
    for script in "${SCRIPTS[@]}"; do
        if [ -f "$script" ]; then
            if [ -x "$script" ]; then
                echo "✅ 脚本有执行权限: $script"
            else
                echo "⚠️  脚本无执行权限: $script (这在CentOS上会自动设置)"
            fi
        fi
    done
    
    echo ""
    echo "5. 检查JAR文件..."
    
    if [ -f "lib/query-1.0-SNAPSHOT.jar" ]; then
        echo "✅ 主JAR文件存在: lib/query-1.0-SNAPSHOT.jar"
        
        # 检查JAR文件是否可以运行
        if command -v java >/dev/null 2>&1; then
            echo "正在测试JAR文件..."
            java -jar lib/query-1.0-SNAPSHOT.jar --help 2>/dev/null
            if [ $? -eq 0 ]; then
                echo "✅ JAR文件可以正常运行"
            else
                echo "⚠️  JAR文件运行测试未通过 (可能需要特定参数)"
            fi
        else
            echo "⚠️  Java未安装，跳过JAR运行测试"
        fi
    else
        echo "❌ 主JAR文件不存在: lib/query-1.0-SNAPSHOT.jar"
    fi
    
    echo ""
    echo "6. 检查依赖文件..."
    
    if [ -d "lib/dependencies" ]; then
        DEP_COUNT=$(ls lib/dependencies/*.jar 2>/dev/null | wc -l)
        echo "✅ 依赖目录存在，包含 $DEP_COUNT 个依赖JAR文件"
    else
        echo "❌ 依赖目录不存在: lib/dependencies"
    fi
    
    if [ -d "lib/test-dependencies" ]; then
        TEST_DEP_COUNT=$(ls lib/test-dependencies/*.jar 2>/dev/null | wc -l)
        echo "✅ 测试依赖目录存在，包含 $TEST_DEP_COUNT 个测试依赖JAR文件"
    else
        echo "❌ 测试依赖目录不存在: lib/test-dependencies"
    fi
    
    echo ""
    echo "7. 检查文档文件..."
    
    DOC_FILES=("docs/TEST_README.md" "docs/DEPLOYMENT_GUIDE.md" "docs/TESTING_SUMMARY.md")
    for doc in "${DOC_FILES[@]}"; do
        if [ -f "$doc" ]; then
            echo "✅ 文档存在: $doc"
        else
            echo "⚠️  文档缺失: $doc"
        fi
    done
    
    echo ""
    echo "8. 显示完整目录结构..."
    echo "----------------------------------------"
    find . -type f | head -20
    echo "... (显示前20个文件)"
    echo "----------------------------------------"
    
    cd ../..
else
    echo "❌ 解压目录不存在"
    cd ..
    rm -rf "$TEMP_DIR"
    exit 1
fi

# 清理临时目录
rm -rf "$TEMP_DIR"

echo ""
echo "=========================================="
echo "验证完成"
echo "=========================================="
echo ""
echo "📦 打包文件信息:"
echo "   主JAR: target/query-1.0-SNAPSHOT.jar ($JAR_SIZE)"
echo "   分发包: target/query-1.0-SNAPSHOT-distribution.tar.gz ($TAR_SIZE)"
echo "   ZIP包: target/query-1.0-SNAPSHOT-distribution.zip ($ZIP_SIZE)"
echo ""
echo "🚀 部署建议:"
echo "   1. 将 target/query-1.0-SNAPSHOT-distribution.tar.gz 上传到CentOS服务器"
echo "   2. 解压: tar -xzf query-1.0-SNAPSHOT-distribution.tar.gz"
echo "   3. 进入目录: cd query-1.0-SNAPSHOT"
echo "   4. 设置权限: chmod +x bin/*.sh"
echo "   5. 运行测试: ./bin/run-tests.sh --all --report"
echo ""
echo "✅ 项目打包验证成功！"
